#!/bin/bash

# set config file
# DEMO RUN help: sh ./run.sh -h
# DEMO RUN with param: sh ./run.sh -f ./config/config2.ini
# PROD RUN: sh ./run.sh -f ./config/config.ini


SCRIPT=$(readlink -f "$0")
SCRIPTPATH=$(dirname "$SCRIPT")
export RMBASE_FILE_PYTHON=$SCRIPTPATH/config/config.ini
echo "Set PYTHONPATH: $SCRIPTPATH"
export PYTHONPATH=$PYTHONPATH:$SCRIPTPATH
export DEBUG=True

# active python poetry if needed
# poetry shell

# run python script
# | tee $SCRIPTPATH/log/run.log
echo "Run python script"
echo $@
python $SCRIPTPATH/src/main.py "$@" 2>&1
