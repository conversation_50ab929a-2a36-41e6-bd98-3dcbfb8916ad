import argparse
import sys
import os
import logging
import toml

# Add the src directory to the Python path
sys.path.append(os.path.abspath(
    os.path.join(os.path.dirname(__file__), '../src')))
from src.components_init import init_mongo
import src.logger as logger

# Suppress debug messages
logging.getLogger('tensorflow').setLevel(logging.WARNING)
logging.getLogger('matplotlib').setLevel(logging.WARNING)
logging.getLogger('h5py._conv').setLevel(logging.WARNING)
logging.getLogger('pymongo').setLevel(logging.WARNING)

# Set up logger
logger.new_logger('mongo_test', 'logs/mongo_test.log', log_level='DEBUG')

def test_mongo_connection(mongo_client):
    try:
        # Perform a simple operation to verify connection
        collections = mongo_client.mongo_db.list_collection_names()
        logger.get_logger().info(
            "MongoDB connection successful. Collections: %s", collections)
    except Exception as e:
        logger.get_logger().error("MongoDB connection test failed: %s", e)
        raise

def main(config):
    mongo_client = None
    try:
        mongo_client = init_mongo(config, logger=logger)
        test_mongo_connection(mongo_client)
    except Exception as e:
        logger.get_logger().error("Failed to test MongoDB connection: %s", e)
        raise
    finally:
        # Close MongoDB client
        if mongo_client:
            mongo_client.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test MongoDB connection.")
    parser.add_argument(
        '-c', '--config',
        default=os.path.join(os.path.dirname(__file__), '../config/config.ini'),
        help='Path to the configuration file (default: ../config/config.ini)'
    )
    args = parser.parse_args()
    try:
        if not os.path.exists(args.config):
            raise FileNotFoundError("Configuration file not found")
        config_all = toml.load(args.config)
        config = config_all['pyfpimg']
        main(config)
    except FileNotFoundError as e:
        logger.get_logger().error("Configuration file error: %s", e)
        sys.exit(2)
    except toml.TomlDecodeError as e:
        logger.get_logger().error("Configuration parsing error: %s", e)
        sys.exit(3)
    except KeyError as e:
        logger.get_logger().error("Missing 'pyfpimg' section in config: %s", e)
        sys.exit(4)
    except Exception as e:
        logger.get_logger().error("Unexpected error: %s", e)
        sys.exit(1)
