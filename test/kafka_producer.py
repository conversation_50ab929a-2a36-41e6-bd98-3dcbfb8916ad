import argparse
import json
import toml
from confluent_kafka import Producer


def delivery_callback(err, msg):
    if err:
        print(f"Message delivery failed: {err}")
    else:
        print(
            f"Message delivered to {msg.topic()} [{msg.partition()}] at offset {msg.offset()}")


def send_message(producer, topic_name, msg):
    try:
        producer.produce(topic_name, value=json.dumps(
            msg).encode('utf-8'), callback=delivery_callback)
        producer.flush()
    except Exception as e:
        print(f"Failed to send message: {e}")



def init_kafka_producer(config):
    bootstrap_servers = config['kafka']['request']['bootstrap_servers']
    conf = {
        'bootstrap.servers': bootstrap_servers,
        'client.id': 'floorplan_classification_producer'
    }
    return Producer(conf)


messages = [
    {
        "_id": "TRBW118188",
        "uaddr": "1 Main St",
        "unt": "101",
        "phoMt": "2023-04-06T15: 33: 31.000Z",
        "pics": [
            "https://f.realmaster.com/P/GYF/GV.jpeg",
            "/trb/1718290348/1/832/W8436832.jpg",
            "trb/828.1718035089/1/230/C8423230.jpg",
        ]
    },
    {
        "_id": "TRBW118189",
        "uaddr": "3 King St",
        "unt": "301",
        "phoMt": "2023-04-06T15: 33: 31.000Z",
        "pics": [
            "//f.realmaster.com/P/GYF/GV.jpeg",
            "trb/1718290348/1/832/W8436832.jpg",
            "/cda/828.1717769520/659/27007659_1.jpg",
            "https://cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCRES/262913989/0/0/0/e537e606990aec2b00601641923daa52/16/e8b27f4103b1011cbf6576428aad0f87/262913989-012A77B8-BA03-41CE-A1D2-6A170394441E.JPG"
        ]
    },
    {
        "_id": "TRBW118187",
        "uaddr": "2 Queen St",
        "unt": "601",
        "phoMt": "2023-04-06T15: 33: 31.000Z",
        "pics": [
            "https://f.realmaster.com/P/GYF/GV.jpeg",
            "/trb/1718290348/1/832/W8436832.jpg",
        ]
    },
    {
        "_id": "TRBW118180",
        "uaddr": "2 Queen St",
        "unt": "601",
        "phoMt": "2023-04-06T15: 33: 31.000Z",
        "pics": [
            "https://f.realmaster.com/P/GYF/GV.jpeg",
            "/cda/828.1717769520/659/27007659_1.jpg",
            "/trb/1718284743/1/950/W8434950.jpg",
            "TRBW118180/5Ktw5zX0Q9.jpg",
            "/TRBW118180/5Ktw5zX0Q9.jpg",
        ]
    }
]
messagesLocal = [
    {
        "_id": "TRBW7025828",
        "uaddr": "1 Main St",
        "unt": "101",
        "phoMt": "2023-09-22T21:42:22.000Z",
        "pics": [
            "GV.jpeg",
            "/trb/1718290348/1/832/W8436832.jpg",
            "trb/828.1718035089/1/230/C8423230.jpg",
        ]
    },
    {
        "_id": "TRBN6799878",
        "uaddr": "3 King St",
        "unt": "301",
        "phoMt": "2023-10-12T13:49:30.000Z",
        "pics": [
            "GV.jpeg",
            # "/trb/1718290348/1/832/W8436832.jpg",
            "/trb/1718290348/1/832/X9517515.jpg",
            # "/cda/828.1717769520/659/27007659_1.jpg",
            "/trb/828.1730983048/4/323/X9414323.jpg",
            "262913989-012A77B8-BA03-41CE-A1D2-6A170394441E.JPG"
        ]
    },
    {
        "_id": "TRBC7054352",
        "uaddr": "2 Queen St",
        "unt": "601",
        "phoMt": "2023-10-03T22:17:14.000Z",
        "pics": [
            "GV.jpeg",
            "/trb/1718290348/1/832/W8436832.jpg",
        ]
    },
    {
        "_id": "TRBC7051312",
        "uaddr": "2 Queen St",
        "unt": "601",
        "phoMt": "2023-10-03T04:17:56.000Z",
        "pics": [
            "GV.jpeg",
            "/cda/828.1717769520/659/27007659_1.jpg",
            "/trb/1718284743/1/950/W8434950.jpg",
        ]
    },
    {
        "_id": "CLGA1002021",
       "uaddr": "CA:AB:RURAL CAMROSE COUNTY:ON RANGE RD 213",
        "unt": "3",
        "phoMt": "2024-09-16T09: 11: 49.470Z",
        "pics": [
            "/clg/828.1726506709/96/99/A1002021_0.jpg",
            "/clg/828.1726506709/96/99/A1002021_2.jpg",
        ]
    },
    {
       "_id": "OTW1134917",
        "uaddr": "CA:ON:OTTAWA:1420 OLD PRESCOTT RD",
        "unt": "1",
        "phoMt": "2019-01-07T05: 47: 29.747Z",
        "pics": [
            "/otw/828.1546876049/29/16/1134917_0.jpg",
            "/otw/828.1546876049/29/16/1134917_1.jpg",
            "/otw/828.1546876049/29/16/1134917_2.jpg"
        ]
    }
]


def main(config):
    try:
        # Kafka configuration
        bootstrap_servers = config['kafka']['request']['bootstrap_servers']
        topic_name_init = config['kafka']['request']['_test_topic_init']
        topic_name_routine = config['kafka']['request']['_test_topic_routine']

        local_paths = {
            'trb': config.get('imageSource', {}).get('local_path_trb'),
            'cda': config.get('imageSource', {}).get('local_path_cda'),
            'clg': config.get('imageSource', {}).get('local_path_creb'),
            'otw': config.get('imageSource', {}).get('local_path_oreb'),
        }
        local_paths = {key: path for key, path in local_paths.items() if path}

        if local_paths:
            testMessage = messagesLocal
        else:
            testMessage = messages
        
        # Create Kafka producer
        conf_init = {
            'bootstrap.servers': bootstrap_servers,
            'client.id': 'floorplan_classification_producer_init'
        }
        conf_routine = {
            'bootstrap.servers': bootstrap_servers,
            'client.id': 'floorplan_classification_producer_routine'
        }
        producer_init = Producer(conf_init)
        producer_routine = Producer(conf_routine)

        for i in range(1):
            for message in testMessage:
                send_message(producer_init, topic_name_init, message)
                send_message(producer_routine, topic_name_routine, message)

    except Exception as e:
        print(f"Failed to send Kafka messages: {e}")
        raise
    finally:
        if producer_init:
            producer_init.flush()
        if producer_routine:
            producer_routine.flush()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Kafka producer script")
    parser.add_argument(
        '-c', '--config',
        default='config/config.ini',
        help='Path to the configuration file (default: config/config.ini)'
    )
    args = parser.parse_args()
    # config = configparser.ConfigParser()
    # config.read(args.config)
    config_all = toml.load(args.config)
    config = config_all['pyfpimg']
    main(config)
