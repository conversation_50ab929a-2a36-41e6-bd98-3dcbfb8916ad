import requests
import google.generativeai as genai
import os
from helpers.AI import AI
import PIL.Image

class Gemini(AI):
    def __init__(self, apiKey: str, model: str, endpoint: str):
        super().__init__(api<PERSON>ey, model, endpoint )

    def generate_response(self, prompt: str, file_dic: dict) -> str:
        """
        Generate a response based on the prompt and the file.

        Args:
            prompt (str): The prompt to generate a response from.
            file_dic (dict): A dictionary containing file data.

        Returns:
            str: The generated response.
        """

        try:
            genai.configure(api_key=self.apiKey)
            model = genai.GenerativeModel(self.model)
            input_list = [prompt]
            if 'images' not in file_dic or not file_dic['images']:
                raise ValueError("No images provided in file_dic")
            for file in file_dic['images']:
                input_list.append(PIL.Image.open(file))
            response = model.generate_content(input_list)

            print(response.text)
            print(response)
            return response.text
        except Exception as e:
            return f"An error occurred: {e}"
