import configparser
from helpers.OpenAI import OpenAI
from helpers.<PERSON> import <PERSON>
from helpers.Gemini import <PERSON>
from components_init import init_AI_config


class AIManager:
    def __init__(self, model_configs: dict):
        """
        Initialize AIManager with model configurations.

        Args:
            model_configs (dict): A dictionary containing model configurations.
        """
        self.ai_instances = self._initialize_ai_instances(model_configs)

    def _initialize_ai_instances(self, model_configs: dict):
        """
        Initialize AI instances based on the provided model configurations.

        Args:
            model_configs (dict): A dictionary containing model configurations.

        Returns:
            dict: A dictionary of AI instances, keyed by model name.
        """
        ai_instances = {}

        # Initialize OpenAI
        if 'openai' in model_configs:
            openai_config = model_configs['openai']
            ai_instances['openai'] = OpenAI(
                apiKey=openai_config.get('key'),
                endpoint=openai_config.get('endpoint'),
                model=openai_config.get('model'),
                orgID=openai_config.get('orgID'),
                projectID=openai_config.get('projectID'),
                batchUrl=openai_config.get('batchUrl')
            )

        # Initialize Gemini
        if 'gemini' in model_configs:
            gemini_config = model_configs['gemini']
            ai_instances['gemini'] = Gemini(
                apiKey=gemini_config.get('key'),
                endpoint=gemini_config.get('endpoint'),
                model=gemini_config.get('model'),
            )

        # Initialize Claude
        if 'claude' in model_configs:
            claude_config = model_configs['claude']
            ai_instances['claude'] = Claude(
                apiKey=claude_config.get('key'),
                endpoint=claude_config.get('endpoint'),
                model=claude_config.get('model'),
            )

        return ai_instances

    def generate_response(self, model_name: str, prompt: str, file_dic: dict) -> str:
        """
        Generate a response using the specified AI model.

        Args:
            model_name (str): The name of the model to use (e.g., 'openai', 'gemini', 'claude').
            prompt (str): The prompt to generate a response from.
            file_dic (dict): A dictionary containing file data.

        Returns:
            str: The generated response.
        """
        ai_instance = self.ai_instances.get(model_name)
        if ai_instance:
            return ai_instance.generate_response(prompt, file_dic)
        raise ValueError(f"Model {model_name} not initialized.")

    def generate_batch_file(self, prompt: str, file_dic: dict, batch_file: str, model_name='openai') -> dict:
        """
        Generate a batch file using the specified AI model.

        Args:
            model_name (str): The name of the model to use (e.g., 'openai', 'gemini', 'claude', only support openai now).
            prompt (str): The prompt to generate a response from.
            file_dic (dict): A dictionary containing file data.

        Returns:
            dict: The generated batch file.
        """
        print("generate_batch_file", model_name)
        ai_instance = self.ai_instances.get(model_name)
        if ai_instance:
            return ai_instance.generate_batch_file(prompt, file_dic, batch_file)
        raise ValueError(f"Model {model_name} not initialized.")


