import requests
from helpers.AI import AI


def generate_messages_from_dic(pdf_dic, prompt):
    """
    Generate messages based on the pdf_list structure.

    Args:
        pdf_dic (dict): A dictionary containing the PDF information.
        prompt (str): The text prompt to include in the messages.

    Returns:
        list: A list of message dictionaries in the required format.
    """
    messages = []
    message_content = []
    message_content.append({
        "type": "text",
        "text": prompt
    })

    for encoded_image in pdf_dic['encoded_images']:
        message_content.append({
            "type": "image",
            "source":{
                "type":"base64",
                "media_type":"image/png",
                "data":encoded_image
            }
        })

    messages.append({
        'role': "user",
        "content": message_content
    })

    return messages

class Claude(AI):
    def __init__(self, apiKey: str, model: str, endpoint: str):
        super().__init__(apiKey, model, endpoint)

    def generate_response(self, prompt: str,  file_dic: str) -> str:
        """
        Generate a response based on the prompt and the file.

        Args:
            prompt (str): The prompt to generate a response from.
            file_dic(dict): A dictionary containing file data.

        Returns:
            str: The generated response.
        """
        headers = {
            'x-api-key': self.apiKey,
            'content-Type': 'application/json',
            "anthropic-version": "2023-06-01"
        }
        messages = generate_messages_from_dic(file_dic, prompt)

        data = {
            'model': self.model,
            'max_tokens': 1024,
            'messages': messages
        }
        print(headers)
        # print(data)

        try:
            response = requests.post(self.endpoint, headers=headers, json=data)
            print(response)
            if response.ok:
                ret = response.json()
                print(ret)
                result = ret['content'][0]['text']
                print(result)
                return result
            raise Exception(f"Error: {response.status_code} - {response.text}")

        except requests.RequestException as e:
            return f"An error occurred: {e}"
