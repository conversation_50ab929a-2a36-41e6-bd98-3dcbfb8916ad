import re
from datetime import datetime
import logging
from pymongo import MongoClient


class MongoDBClient:
    def __init__(self, mongo_uri, mongo_db, mongo_collection):
        self.mongo_client = MongoClient(mongo_uri)
        self.mongo_db = self.mongo_client[mongo_db]
        self.mongo_collection = self.mongo_db[mongo_collection]

    def write(self, result: dict) -> None:
        """
        Write the result JSON to MongoDB.

        Args:
            result (dict): The result dictionary to write.

        Returns:
            None
        """
        _id = result["_id"]
        uaddr = result["uaddr"]
        unt = result["unt"]
        new_prop_id = _id
        phoMt_str = re.sub(r'\s+', '', result["phoMt"])
        phoMt = datetime.strptime(phoMt_str, "%Y-%m-%dT%H:%M:%S.%fZ")

        # Get current time
        current_time = datetime.utcnow()

        # Filter out entries with an error and add ts to each entry
        filtered_results = [
            {**entry, "ts": current_time}
            for entry in result["result"] if "error" not in entry
        ]

        update_query = {
            "_id": f"{uaddr}#{unt}"
        }
        update_operations = {
            "$set": {
                "uaddr": uaddr,
                "unt": unt,
                "phoMt": phoMt
            },
            "$addToSet": {
                "prop_id": new_prop_id,
            },
            "$push": {
                "result": {"$each": filtered_results}
            }
        }

        # Use update_one with upsert=True to update or insert the document
        self.mongo_collection.update_one(
            update_query, update_operations, upsert=True)
        logging.info("Result written to MongoDB")

    def close(self):
        """
        Close the MongoDB client instance if it exists.
        """
        if self.mongo_client:
            self.mongo_client.close()
            self.mongo_client = None
