import re
from datetime import datetime
import logging
from pymongo import MongoClient


class MongoDBClient:
    def __init__(self, mongo_uri, mongo_db, mongo_collection):
        self.mongo_client = MongoClient(mongo_uri)
        self.mongo_db = self.mongo_client[mongo_db]
        self.mongo_collection = self.mongo_db[mongo_collection]

    def write(self, result: dict) -> None:
        """
        Write the result JSON to MongoDB.

        Args:
            result (dict): The result dictionary to write.

        Returns:
            None
        """
        _id = result["_id"]
        uaddr = result["uaddr"]
        unt = result["unt"]
        new_prop_id = _id
        phoMt_str = re.sub(r'\s+', '', result["phoMt"])
        phoMt = datetime.strptime(phoMt_str, "%Y-%m-%dT%H:%M:%S.%fZ")

        # Get current time
        current_time = datetime.utcnow()

        # Filter out entries with an error and add ts to each entry
        filtered_results = [
            {**entry, "ts": current_time}
            for entry in result["result"] if "error" not in entry
        ]

        update_query = {
            "_id": f"{uaddr}#{unt}"
        }
        update_operations = {
            "$set": {
                "uaddr": uaddr,
                "unt": unt,
                "phoMt": phoMt
            },
            "$addToSet": {
                "prop_id": new_prop_id,
            },
            "$push": {
                "result": {"$each": filtered_results}
            }
        }

        # Use update_one with upsert=True to update or insert the document
        self.mongo_collection.update_one(
            update_query, update_operations, upsert=True)
        logging.info("Result written to MongoDB")

    def write_list(self, results: list) -> None:
        """
        Write a list of result JSONs to MongoDB. If a result already exists, update it; otherwise, create a new one.

        Args:
            results (list): List of result dictionaries to write.

        Returns:
            None
        """
        for result in results:
            current_time = datetime.utcnow()
            result['ts'] = current_time
            # Assuming each result has a unique identifier, e.g., 'pdf_name' or 'pdf_path'
            filter_query = {
                '_id': result['_id']
            }

            # Perform an upsert (update if exists, insert if not)
            self.mongo_collection.update_one(
                filter_query,  # Match the document by 'pdf_name' and 'pdf_path'
                {"$set": result},  # Set the document fields
                upsert=True  # Create the document if it doesn't exist
            )
        logging.info(f"{len(results)} results processed and written to MongoDB")


    def update_ai_result(self, id: str, result: list) -> None:
        """
        Update the AI result in MongoDB.

        Args:
            id (str): The unique identifier of the document to update.
            result (list): The AI result to update.

        Returns:
            None
        """
        print(f"######Updating AI result for {id} with result: {result}")
        if not isinstance(result, list):
            raise ValueError("Result must be a list.")

        update_fields = {
            'ai_results': result,
            'ts': datetime.utcnow()
        }
        filter_query = {'_id': id}
        try:
            self.mongo_collection.update_one(
                filter_query,
                {"$set": update_fields},
                upsert=True
            )
            logging.info("AI result for '%s' updated in MongoDB at %s.",
                         id, datetime.utcnow().isoformat())
        except Exception as e:
            logging.error(
                "Failed to update AI result for '%s' in MongoDB: %s", id, e)


    def findOne(self, query=None) -> dict:
        """
        Find a floorplanC by address, city, province, and project name.

        Args:
            query (dict): The query to search for the floor plan document.

        Returns:
            dict: The floor plan document.
        """    
        if query is None:
            query = {}
        # Find the document based on the query
        floorplan = self.mongo_collection.find_one(query)
        if floorplan:
            return floorplan
        return None
    
    
    def find(self, query=None, limit=1000) -> list:
        """
        Find all floor plans that match the query.

        Args:
            query (dict): The query to search for the floor plan documents.
            limit (int): The maximum number of documents to return.

        Returns:
            list: The list of floor plan documents.
        """
        if query is None:
            query = {}
        # Find the documents based on the query
        floorplans = self.mongo_collection.find(query).limit(limit)
        return list(floorplans)


    def close(self):
        """
        Close the MongoDB client instance if it exists.
        """
        if self.mongo_client:
            self.mongo_client.close()
            self.mongo_client = None
