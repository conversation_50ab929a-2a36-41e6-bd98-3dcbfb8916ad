import time

#  speed calculation
#  usage:
#     speedMeter = SpeedMeter()
#     speedMeter.check {meter1:13,meter2:200}
#     speedMeter.check {meter1:10,meter3:10}
#     speedMeter.getSpeed() => {meter1:9.3,meter2:30,unit:'s|m|h'}
#     speedMeter.toString(unit='s') => meter1:9.3/s
#     speedMeter.estimate({meter1:1000(total-value)},unit='h')
#     speedMeter.reset( {meter1:123} or null)


UNIT_MAP = {
    'ms': 1,
    's': 1000,
    'm': 60000,
    'h': 3600000,
    'd': 3600000 * 24
}

AMOUNT_UNIT = 'KMBT'


class SpeedMeter:
    def __init__(self, values=None, interval_callback=None, interval_trigger_count=1000):
        """
        Initialize a SpeedMeter instance.

        Args:
            values (dict): Initial values for the speed calculation.
            interval_callback (function): A callback function to be called at each interval.
            interval_trigger_count (int): Number of checks before triggering the interval callback.
        """
        self.interval_trigger_count = interval_trigger_count
        self.interval_callback = interval_callback
        self._counter = 0
        self.reset(values)

    def reset(self, values=None):
        """
        Reset the SpeedMeter with new initial values.

        Args:
            values (dict): New initial values for the speed calculation.
        """
        self.ts = time.time() * 1000  # in ms
        self.values = values or {}

    def check(self, values):
        """
        Update the SpeedMeter with new values.

        Args:
            values (dict): Values to add to the current speed calculations.
        """
        for k, v in values.items():
            self.values[k] = self.values.get(k, 0) + v
        self._counter += 1
        if self.interval_callback and (self._counter % self.interval_trigger_count) == 0:
            self.interval_callback(self)

    def get_speed(self, unit='m'):
        """
        Calculate the speed of the monitored values.

        Args:
            unit (str): Unit of time for speed calculation ('ms', 's', 'm', 'h', 'd').

        Returns:
            dict: The speed of each monitored value.
        """
        denominator = UNIT_MAP[unit]
        speed = self.values.copy()
        ms = (time.time() * 1000 - self.ts) or 1  # prevent divide by 0
        for k, v in speed.items():
            speed[k] = v * denominator / ms
        return speed

    def to_string(self, unit='m', to_be_estimated=None):
        """
        Convert the speed data to a string representation.

        Args:
            unit (str): Unit of time for speed calculation ('ms', 's', 'm', 'h', 'd').
            to_be_estimated (dict): Values to estimate the remaining time.

        Returns:
            str: The string representation of the speed data.
        """
        speed = self.get_speed(unit)
        estimates = {}
        if to_be_estimated:
            estimates = self.estimate(to_be_estimated, unit)
        ary = []
        for k, v in speed.items():
            v = self._number_to_show(v)
            to_show = f"{k}({self.values[k]}):{v}/{unit}"
            if e := estimates.get(k):
                to_show += f" est:{self._number_to_show(e)} {unit}"
            ary.append(to_show)
        return ', '.join(ary)

    def _number_to_show(self, v):
        """
        Format a number for display with appropriate units.

        Args:
            v (float): The number to format.

        Returns:
            str: The formatted number.
        """
        amount_unit = ''
        amount_unit_at = 0
        while v > 1000:
            v /= 1000
            amount_unit = AMOUNT_UNIT[amount_unit_at]
            amount_unit_at += 1
        return f"{v:.3g}{amount_unit}"  # 999.9K ~ 9.999K

    def estimate(self, total_values, unit='h'):
        """
        Estimate the remaining time to reach given values.

        Args:
            total_values (dict): Total values to estimate the remaining time.
            unit (str): Unit of time for estimation ('ms', 's', 'm', 'h', 'd').

        Returns:
            dict: Estimated time to reach each value.
        """
        estimates = total_values.copy()
        speeds = self.get_speed(unit)
        for k, v in estimates.items():
            estimates[k] = max((v - self.values.get(k, 0)), 0) / speeds[k]
        return estimates

    def get_counters(self):
        """
        Get the current counter values.

        Returns:
            dict: The current counter values.
        """
        return self.values.copy()