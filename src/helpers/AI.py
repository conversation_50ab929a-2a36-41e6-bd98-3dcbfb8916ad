from abc import ABC, abstractmethod


class AI(ABC):
    def __init__(self, apiKey: str, model: str, endpoint: str = None, ):
        self.apiKey = apiKey
        self.model = model
        self.endpoint = endpoint

    @abstractmethod
    def generate_response(self, prompt: str, file_dic: dict) -> str:
        """
        Generate a response based on the prompt.

        Args:
            prompt (str): The prompt to generate a response from.
            file_dic (dict): A dictionary containing file data.
        """
        pass