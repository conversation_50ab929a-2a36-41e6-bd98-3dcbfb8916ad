import requests
import json
from helpers.AI import AI
import logging


def generate_messages_from_dic(pdf_dic, prompt):
    """
    Generate messages based on the pdf_list structure.

    Args:
        pdf_dic (dict): A dictionary containing the PDF information.
        prompt (str): The text prompt to include in the messages.

    Returns:
        list: A list of message dictionaries in the required format.
    """
    messages = []
    message_content = []
    message_content.append({
          "type": "text",
          "text": prompt
          })

    for encoded_image in pdf_dic['encoded_images']:
        message_content.append({
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{encoded_image}"
            }
        })
        # break

    messages.append({
        'role': "user",
        "content": message_content
    })

    return messages
 
        
class OpenAI(AI):
    def __init__(self, apiKey: str, model: str, endpoint: str, orgID: str, projectID: str, batchUrl: str):
        super().__init__(apiKey, model, endpoint)
        self.orgID = orgID
        self.projectID = projectID
        self.batchUrl = batchUrl
    
    def generate_response(self, prompt: str, file_dic: dict) -> str:
        """
        Generate a response based on the prompt and the file.

        Args:
            prompt (str): The prompt to generate a response from.
            file_dic (dict): A dictionary containing file data.

        Returns:
            str: The generated response.
        """

        headers = {
            'Authorization': f'Bearer {self.apiKey}',
            'Content-Type': 'application/json',
            'OpenAI-Organization': self.orgID,
            'OpenAI-Project': self.projectID
        }

        messages = generate_messages_from_dic(file_dic, prompt)

        data = {
            'model': self.model,
            'messages': messages,
            'stream': False
        }

        # Sending a POST request to OpenAI API (simulated)
        try:
            response = requests.post(self.endpoint, headers=headers, json=data)
            logging.debug(f"OpenAI API response status: {response.status_code}")
            if response.ok:
                ret = response.json()
                logging.debug(f"OpenAI API response: {ret}")
                result = ret['choices'][0]['message']['content']
                logging.debug(f"Extracted result: {result}")
                return result
            else:
                error_msg = f"Error: {response.status_code} - {response.text}"
                logging.error(error_msg)
                return error_msg

        except requests.RequestException as e:
            error_msg = f"An error occurred: {e}"
            logging.error(error_msg)
            return error_msg

    def generate_batch_file(self, prompt: str, file_dic: dict, batch_file: str) -> bool:
        """
        Generate a batch file based on the prompt and the file.

        Args:
            prompt (str): The prompt to generate a response from.
            file_dic (dict): A dictionary containing file data.
            batch_file (str): The path to the batch file to generate.

        Returns:
            bool: True if the batch file was generated successfully, False otherwise.
        """
        messages = generate_messages_from_dic(file_dic, prompt)

        data = {
            'custom_id': file_dic['_id'],
            'method': 'POST',
            'url': self.batchUrl,
            'body': {
                'model': self.model,
                'messages': messages,
                "max_tokens": 4096
            }
        }

        try:
            with open(batch_file, 'a') as f:
                json.dump(data, f)
                f.write('\n')
            return True
        except Exception as e:
            logging.error(f"An error occurred: {e}")
            return False
