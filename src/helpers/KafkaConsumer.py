import logging
from confluent_kafka import Consumer


class KafkaConsumer:
    def __init__(self, bootstrap_servers: str, group_id: str, auto_offset_reset: str, topic: str, timeout: float = 60.0):
        """
        Initialize the Kafka consumer instance and subscribe to topics.

        Args:
            bootstrap_servers (str): The Kafka broker address.
            group_id (str): A unique string that identifies the consumer group this consumer belongs to.
            auto_offset_reset (str): The strategy to use when there is no initial offset in Kafka or if the current offset does not exist any more.
            topics (str): Kafka topic to subscribe to.
            timeout (float): Timeout value in seconds for polling messages.
        """
        self.consumer_conf = {
            'bootstrap.servers': bootstrap_servers,
            'group.id': group_id,
            'auto.offset.reset': auto_offset_reset,
            'session.timeout.ms': 60000,  # Increase session timeout
            'heartbeat.interval.ms': 20000,  # Increase heartbeat interval
            'max.poll.interval.ms': 600000,  # Increase max poll interval
        }
        self.consumer = Consumer(self.consumer_conf)
        self.topic = topic
        self.consumer.subscribe([self.topic])
        self.timeout = timeout

    def poll(self):
        """
        Poll messages from the subscribed Kafka topic.

        Args:
            None

        Returns:
            msg (object): Kafka message object.
        """
        msg = self.consumer.poll(self.timeout)
        if msg is not None:
            logging.info("Received message with offset %s in group %s",
                         msg.offset(), self.consumer_conf['group.id'])
        return msg

    def close(self):
        """
        Close the Kafka consumer.
        """
        try:
            self.consumer.close()
        except Exception as e:
            logging.error("Error closing Kafka consumer: %s", e)
            raise
