import json
import logging
from confluent_kafka import Producer


class KafkaProducer:
    def __init__(self, bootstrap_servers, topic):
        """
        Initialize the Kafka producer instance.
        Args:
            bootstrap_servers (str): The Kafka bootstrap servers to connect to.
            topic (str): The Kafka topic to send messages to.
        """
        if not bootstrap_servers or not topic:
            raise ValueError("Bootstrap servers and topic must not be empty")

        producer_conf = {
            'bootstrap.servers': bootstrap_servers,
            'client.id': 'python-producer',
            'retries': 3,
            'retry.backoff.ms': 1000,
        }
        try:
            self.producer = Producer(producer_conf)
        except Exception as e:
            logging.error("Failed to create Kafka producer: %s", str(e))
            raise
        self.topic = topic

    def produce(self, message: dict) -> None:
        """
        Send a JSON message to a Kafka topic.
        Args:
            message (dict): The JSON message to send.
        Returns:
            None
        """
        try:
            json_message = json.dumps(message)
            self.producer.produce(self.topic, json_message.encode('utf-8'))
            self.producer.flush()
            logging.info("Message sent to Kafka topic %s",
                        self.topic)
        except Exception as e:
            logging.error("Failed to send message to Kafka: %s", str(e))

    def close(self):
        """
        Close the Kafka producer instance if it exists.
        """
        if self.producer:
            self.producer.flush()
            self.producer = None
