import openai
import time
import logging
from openai import APIConnectionError

# Initialize OpenAI client
client = ''


def create_client(api_key):
    """Create an OpenAI client."""
    global client
    client = openai.OpenAI(api_key=api_key)
    return client


def upload_batch_input_file(file_path, max_retries=3, retry_delay=2):
    """Upload a batch input file to OpenAI with retry mechanism."""
    for attempt in range(max_retries):
        try:
            with open(file_path, "rb") as f:
                batch_input_file = client.files.create(
                    file=f,
                    purpose="batch"
                )
            batch_input_file_id = batch_input_file.id
            logging.info(f"File uploaded successfully on attempt {attempt + 1}: {batch_input_file_id}")
            return batch_input_file_id

        except (APIConnectionError, Exception) as e:
            if attempt < max_retries - 1:
                logging.warning(f"Upload attempt {attempt + 1} failed: {e}. Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                logging.error(f"All {max_retries} upload attempts failed. Last error: {e}")
                raise e


def create_batch(batch_input_file_id, description="batch job", max_retries=3, retry_delay=2):
    """Create a batch job with the uploaded input file with retry mechanism."""
    for attempt in range(max_retries):
        try:
            batch = client.batches.create(
                input_file_id=batch_input_file_id,
                endpoint="/v1/chat/completions",
                completion_window="24h",
                metadata={
                    "description": description
                }
            )
            batch_id = batch.id
            logging.info(f"Batch created successfully on attempt {attempt + 1}: {batch_id}")
            return batch_id

        except (APIConnectionError, Exception) as e:
            if attempt < max_retries - 1:
                logging.warning(f"Batch creation attempt {attempt + 1} failed: {e}. Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                logging.error(f"All {max_retries} batch creation attempts failed. Last error: {e}")
                raise e


def check_batch_status(batch_id):
    """Retrieve the status of a batch."""
    batch_status = client.batches.retrieve(batch_id)
    # batch_status:status='in_progress';status='completed'
    # Batch(id='batch_5okupcIqsdmPwOh6W6SrDLOy', completion_window='24h', created_at=1725737381, endpoint='/v1/chat/completions', input_file_id='file-60XJENqFsYbqKOLAogNxTn6y', object='batch', status='in_progress', cancelled_at=None, cancelling_at=None, completed_at=None,
    #                     error_file_id=None, errors=None, expired_at=None, expires_at=1725823781, failed_at=None, finalizing_at=None, in_progress_at=1725737383, metadata={'description': 'batch job'}, output_file_id=None, request_counts=BatchRequestCounts(completed=0, failed=0, total=1))
    return batch_status


def retrieve_batch_results(output_file_id):
    """Retrieve batch output results."""
    if not output_file_id:
        raise ValueError("File ID cannot be None or empty")
    file_response = client.files.content(output_file_id)
    logging.info(f"File response: {file_response.text}")
    return file_response.text


def cancel_batch(batch_id):
    """Cancel an ongoing batch."""
    cancel_response = client.batches.cancel(batch_id)
    return cancel_response


def list_batches(limit=10):
    """Retrieve a list of all batches."""
    batches_list = client.batches.list(limit=limit)
    return batches_list


def get_detailed_batch_info(batch_id):
    """Get detailed information about a batch including all error details."""
    batch_status = client.batches.retrieve(batch_id)
    
    detailed_info = {
        'id': batch_status.id,
        'status': batch_status.status,
        'created_at': batch_status.created_at,
        'metadata': batch_status.metadata,
        'endpoint': batch_status.endpoint,
        'completion_window': batch_status.completion_window
    }
    
    # Add error-related information
    if hasattr(batch_status, 'errors') and batch_status.errors:
        detailed_info['errors'] = batch_status.errors
    
    if hasattr(batch_status, 'failed_at') and batch_status.failed_at:
        detailed_info['failed_at'] = batch_status.failed_at
    
    if hasattr(batch_status, 'error_file_id') and batch_status.error_file_id:
        detailed_info['error_file_id'] = batch_status.error_file_id
        try:
            error_content = retrieve_batch_results(batch_status.error_file_id)
            detailed_info['error_file_content'] = error_content
        except Exception as e:
            detailed_info['error_file_retrieval_error'] = str(e)
    
    if hasattr(batch_status, 'request_counts') and batch_status.request_counts:
        detailed_info['request_counts'] = {
            'total': batch_status.request_counts.total,
            'completed': batch_status.request_counts.completed,
            'failed': batch_status.request_counts.failed
        }
    
    if hasattr(batch_status, 'validation_errors') and batch_status.validation_errors:
        detailed_info['validation_errors'] = batch_status.validation_errors
    
    if hasattr(batch_status, 'expired_at') and batch_status.expired_at:
        detailed_info['expired_at'] = batch_status.expired_at
    
    if hasattr(batch_status, 'cancelled_at') and batch_status.cancelled_at:
        detailed_info['cancelled_at'] = batch_status.cancelled_at
    
    return detailed_info


def check_batch_status_cli(batch_id, api_key):
    """Command line utility to check batch status and get detailed error information."""
    try:
        create_client(api_key)
        detailed_info = get_detailed_batch_info(batch_id)
        
        print(f"Batch ID: {detailed_info['id']}")
        print(f"Status: {detailed_info['status']}")
        print(f"Created at: {detailed_info['created_at']}")
        print(f"Endpoint: {detailed_info['endpoint']}")
        print(f"Metadata: {detailed_info['metadata']}")
        
        if detailed_info['status'] == 'failed':
            print("\n=== FAILURE DETAILS ===")
            if 'failed_at' in detailed_info:
                print(f"Failed at: {detailed_info['failed_at']}")
            
            if 'errors' in detailed_info:
                print(f"Errors: {detailed_info['errors']}")
            
            if 'request_counts' in detailed_info:
                counts = detailed_info['request_counts']
                print(f"Request counts - Total: {counts['total']}, Completed: {counts['completed']}, Failed: {counts['failed']}")
            
            if 'error_file_content' in detailed_info:
                print(f"\nError file content:\n{detailed_info['error_file_content']}")
            
            if 'validation_errors' in detailed_info:
                print(f"Validation errors: {detailed_info['validation_errors']}")
        
        elif detailed_info['status'] == 'completed':
            print("\n=== SUCCESS DETAILS ===")
            if 'request_counts' in detailed_info:
                counts = detailed_info['request_counts']
                print(f"Request counts - Total: {counts['total']}, Completed: {counts['completed']}, Failed: {counts['failed']}")
        
        return detailed_info
        
    except Exception as e:
        print(f"Error checking batch status: {e}")
        return None


if __name__ == "__main__":
    # Step 1: Upload your batch input file
    batch_input_file = upload_batch_input_file("batchinput.jsonl")
    batch_input_file_id = batch_input_file.id
    print(f"Uploaded batch input file ID: {batch_input_file_id}")

    # Step 2: Create a batch using the uploaded file
    batch = create_batch(batch_input_file_id, description="nightly eval job")
    batch_id = batch.id
    print(f"Created batch ID: {batch_id}")

    # Step 3: Check the status of the batch
    status = check_batch_status(batch_id)
    print(f"Batch status: {status['status']}")

    # Optionally, cancel the batch if needed
    # cancel_response = cancel_batch(batch_id)
    # print(f"Batch cancel response: {cancel_response}")

    # Step 4: Retrieve the results once the batch is completed
    if status['status'] == 'completed':
        output_file_id = status['output_file_id']
        results = retrieve_batch_results(output_file_id)
        print(f"Batch results: {results}")

    # Step 5: Get a list of all batches
    batches = list_batches(limit=5)
    print(f"All batches: {batches}")
