import json
import time
import logger
from helpers.AIManager import AIManager
from src.floorplan_classification.image_wrangling import remove_file
from src.helpers.openAI_batch import check_batch_status, create_batch, create_client, retrieve_batch_results, upload_batch_input_file

def generate_batch_file_from_pdf_list(prompt: str, pdf_list: list, batch_file: str, manager: AIManager):
    """
    Generate a batch file based on the prompt and the list of PDFs.

    Args:
        prompt (str): The prompt to generate a response from.
        pdf_list (list): List of dictionaries containing PDF data.
        batch_file (str): The path to the batch file to generate.
        manager (AIManager): The AIManager instance to generate the batch file.
    """
    remove_file(batch_file)
    for pdf_dict in pdf_list:
        try:
            manager.generate_batch_file(
                prompt, pdf_dict, batch_file, 'openai')
        except ValueError as e:
            error_message = f"Error generating batch file for {pdf_dict['pdf_name']}: {e}"
            logger.get_logger().error(error_message)
            raise
    logger.get_logger().info(
        f"Batch file generated successfully: {batch_file}")


def process_and_check_batch(api_key, batch_file, mongo_client, pdf_list=None):
    """
    process the batch file and check the status of the batch job

    Args:
        api_key (str): The OpenAI API key.
        batch_file (str): The path to the batch file.
        mongo_client (MongoDBClient): The MongoDB client instance.
        pdf_list (list, optional): List of PDF dictionaries for validation.
    """
    create_client(api_key)

    # Create a mapping of PDF ID to input image count for validation
    pdf_image_counts = {}
    if pdf_list:
        for pdf_dict in pdf_list:
            pdf_id = pdf_dict.get('_id')
            image_count = len(pdf_dict.get('encoded_images', []))
            if pdf_id:
                pdf_image_counts[pdf_id] = image_count

    # upload batch file
    batch_input_file_id = upload_batch_input_file(batch_file)
    if not batch_input_file_id:
        error_message = f"Error uploading batch input file {batch_file}"
        logger.get_logger().error(error_message)
        raise Exception(error_message)

    # create batch job
    batch_id = create_batch(batch_input_file_id,
                            description="batch job #batch_file")
    if not batch_id:
        error_message = f"Error creating batch job for {batch_file}"
        logger.get_logger().error(error_message)
        raise Exception(error_message)
    logger.get_logger().info(f"Batch job {batch_id} created.")

    # Define the wait intervals in seconds:1min, 1min, 2min, 5min, 10 min, 30 min, then 1 hour repeatedly(for total 24h)
    wait_intervals = [60, 60, 120, 300, 600, 1800] + [3600] * 24
    total_wait_time = 0
    wait_intervals_index = 0
    max_total_wait_time = 24 * 3600

    # check batch status
    while True:
        batch_status = check_batch_status(batch_id)
        logger.get_logger().info(f"Batch job {batch_id} status: {batch_status.status}")
        
        if batch_status.status == 'completed':
            if not batch_status.output_file_id:
                error_message = f"Batch job {batch_id} completed but no output file available"
                logger.get_logger().error(error_message)
                raise Exception(error_message)
            results = retrieve_batch_results(batch_status.output_file_id)
            logger.get_logger().info(
                f"Batch job {batch_id} completed. Processing results...")
            process_batch_result(results, mongo_client, pdf_image_counts)
            break
        elif batch_status.status == 'failed':
            error_message = f"Batch job {batch_id} failed, metadata: {batch_status.metadata}"
            
            # Add detailed error information
            if hasattr(batch_status, 'errors') and batch_status.errors:
                error_message += f"\nErrors: {batch_status.errors}"
            
            if hasattr(batch_status, 'failed_at') and batch_status.failed_at:
                error_message += f"\nFailed at: {batch_status.failed_at}"
            
            if hasattr(batch_status, 'request_counts') and batch_status.request_counts:
                error_message += f"\nRequest counts: {batch_status.request_counts}"
            
            # Try to get error file if available
            if batch_status.error_file_id:
                try:
                    error_result = retrieve_batch_results(batch_status.error_file_id)
                    error_message += f"\nError file content:\n{error_result}"
                except Exception as e:
                    error_message += f"\nError retrieving error file: {e}"
            else:
                error_message += "\nNo error file available"
            
            # Add validation errors if any
            if hasattr(batch_status, 'validation_errors') and batch_status.validation_errors:
                error_message += f"\nValidation errors: {batch_status.validation_errors}"
            
            logger.get_logger().error(error_message)
            raise Exception(error_message)
        elif batch_status.status == 'expired':
            error_message = f"Batch job {batch_id} expired"
            logger.get_logger().error(error_message)
            raise Exception(error_message)
        elif batch_status.status == 'cancelled':
            error_message = f"Batch job {batch_id} was cancelled"
            logger.get_logger().error(error_message)
            raise Exception(error_message)
        else:
            wait_time = wait_intervals[wait_intervals_index]
            wait_in_minutes = wait_time // 60
            logger.get_logger().info(
                f"Batch job {batch_id} still in progress. Waiting {wait_in_minutes} minutes before retry...")
            time.sleep(wait_time)
            total_wait_time += wait_time
            wait_intervals_index += 1
            if total_wait_time >= max_total_wait_time:
                error_message = f"Batch job {batch_id} did not complete within 24 hours."
                logger.get_logger().error(error_message)
                raise Exception(error_message)


def process_batch_result(results, mongo_client, pdf_image_counts=None):
    """
    Process the batch result and store it in MongoDB.

    Args:
        results (str): The batch result string.
        mongo_client (MongoDBClient): The MongoDB client instance.
        pdf_image_counts (dict, optional): Mapping of PDF ID to input image count.

    Returns:
        bool: True if the batch result was processed successfully, False otherwise.
    """
    try:
        result_list = results.split('\n')
        for result in result_list:
            if result:
                result_json = json.loads(result)
                id = result_json['custom_id']
                ai_result_str = result_json['response']['body']['choices'][0]['message']['content']
                ai_result_str = ai_result_str.replace('\n', '').replace(
                    '\r', '').replace('```json', '').replace('```', '').strip()
                logger.get_logger().debug(
                    f"AI Result for {id}: {ai_result_str}")
                ai_result = json.loads(ai_result_str)

                # Ensure ai_result is always a list (array)
                # Since one PDF can contain multiple images, we expect an array
                if isinstance(ai_result, dict):
                    # If AI returned a single object instead of array, wrap it in a list
                    ai_result = [ai_result]
                    logger.get_logger().warning(f"AI returned single object for {id}, wrapped in array")
                elif isinstance(ai_result, list):
                    # Already a list, this is expected
                    # Check if the number of returned items matches the number of input images
                    returned_count = len(ai_result)
                    expected_count = pdf_image_counts.get(id) if pdf_image_counts else None

                    if expected_count is not None:
                        if returned_count == expected_count:
                            logger.get_logger().debug(f"AI returned array with {returned_count} items for {id} (matches input)")
                        else:
                            logger.get_logger().warning(f"AI returned array with {returned_count} items for {id}, but expected {expected_count} (input images count mismatch)")
                    else:
                        logger.get_logger().debug(f"AI returned array with {returned_count} items for {id} (no input count available for validation)")
                else:
                    # Unexpected format
                    logger.get_logger().error(f"Unexpected AI result type for {id}: {type(ai_result)}")
                    continue

                mongo_client.update_ai_result(id, ai_result)
        return True
    except json.JSONDecodeError as e:
        error_message = f"Error decoding JSON result: {e}"
        logger.get_logger().error(error_message)
        return False
