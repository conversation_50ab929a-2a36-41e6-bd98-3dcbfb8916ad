import sys
import logger



def init_image_source(config):
    """
    Initialize image source.

    Args:
        config (ConfigParser): Configuration object.

    Returns:
        source_type: Image source type.
        img_source: Image source.
    """
    try:
        local_path_value = config.get(
            'imageSource', 'local_path', fallback=None)
        if local_path_value and local_path_value.strip() != "":
            return 'local', config.get('imageSource', 'local_path')
        if config.has_option('imageSource', 'url_host'):
            return 'url', config.get('imageSource', 'url_host')
    except Exception as e:
        error_msg = f"Error configuring image source: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e


def graceful_exit(signal, frame, kafka_producer=None, mongo_client=None, model_manager=None, kafka_consumer_routine=None, kafka_consumer_init=None, should_continue=None):
    """
    Gracefully exit the application.

    Args:
        signal: The signal number.
        frame: The current stack frame.

    Returns:
        None
    """

    logger.get_logger().info("Exiting...")
    if should_continue:
        should_continue['running'] = False
    if kafka_consumer_routine:
        kafka_consumer_routine.close()
        logger.get_logger().debug("Kafka consumer routine closed.")
    if kafka_consumer_init:
        kafka_consumer_init.close()
        logger.get_logger().debug("Kafka consumer init closed.")
    if kafka_producer:
        kafka_producer.close()
        logger.get_logger().debug("Kafka producer closed.")
    if mongo_client:
        mongo_client.close()
        logger.get_logger().debug("MongoDB client closed.")
    if model_manager:
        model_manager.close()
        logger.get_logger().debug("Model manager closed.")
    sys.exit(0)

def signal_handler_SIGUSR2(sig, frame, img_count):
    """
    Signal handler for SIGUSR2 signal.

    Args:
        sig: The signal number.
        frame: The current stack frame.
        img_count (Value): Shared Value for counting images processed.

    Returns:
        None
    """
    print("---- in SIGUSR2 handler: \n")
    print("Received signal SIGUSR2\n")
    print("img_count.value: ", img_count.value, "\n")