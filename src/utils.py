import re
import sys
import uuid
import nanoid
import logger



def init_image_source(config):
    """
    Initialize image source.

    Args:
        config (ConfigParser): Configuration object.

    Returns:
        source_type: Image source type.
        img_source: Image source.
    """
    try:
        local_path_value = config.get(
            'imageSource', 'local_path', fallback=None)
        if local_path_value and local_path_value.strip() != "":
            return 'local', config.get('imageSource', 'local_path')
        if config.has_option('imageSource', 'url_host'):
            return 'url', config.get('imageSource', 'url_host')
    except Exception as e:
        error_msg = f"Error configuring image source: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e


def graceful_exit(signal, frame, kafka_producer=None, mongo_client=None, model_manager=None, kafka_consumer_routine=None, kafka_consumer_init=None, should_continue=None):
    """
    Gracefully exit the application.

    Args:
        signal: The signal number.
        frame: The current stack frame.

    Returns:
        None
    """

    logger.get_logger().info("Exiting...")
    if should_continue:
        should_continue['running'] = False
    if kafka_consumer_routine:
        kafka_consumer_routine.close()
        logger.get_logger().debug("Kafka consumer routine closed.")
    if kafka_consumer_init:
        kafka_consumer_init.close()
        logger.get_logger().debug("Kafka consumer init closed.")
    if kafka_producer:
        kafka_producer.close()
        logger.get_logger().debug("Kafka producer closed.")
    if mongo_client:
        mongo_client.close()
        logger.get_logger().debug("MongoDB client closed.")
    if model_manager:
        model_manager.close()
        logger.get_logger().debug("Model manager closed.")
    sys.exit(0)

def signal_handler_SIGUSR2(sig, frame, img_count):
    """
    Signal handler for SIGUSR2 signal.

    Args:
        sig: The signal number.
        frame: The current stack frame.
        img_count (Value): Shared Value for counting images processed.

    Returns:
        None
    """
    print("---- in SIGUSR2 handler: \n")
    print("Received signal SIGUSR2\n")
    print("img_count.value: ", img_count.value, "\n")


def generate_unique_id():
    """
    Add a unique _id to each dictionary in the data list.


    Returns:
        str: A unique identifier.
    """

    return str(uuid.uuid4())


def parse_name(name):
    """
    Parse the name of the PDF file to extract the address, city, province, and project name.

    Args:
        name (str): The name of the PDF file.

    Returns:
        tuple: The address, city, province, and project name.
    """
    # Remove file extension first
    name_without_ext = re.sub(r'\.[^.]*$', '', name)

    # Try multiple patterns in order of specificity
    patterns = [
        # Pattern 1: Address_City_Province-Project (underscore separators with dash for project)
        r'^(?P<addr>.+?)_(?P<city>[A-Za-z][A-Za-z0-9]*?)_(?P<prov>[A-Za-z]{2,3})-(?P<project_name>.+)$',

        # Pattern 2: Address_City_Province (underscore separators, no project)
        r'^(?P<addr>.+?)_(?P<city>[A-Za-z][A-Za-z0-9]*?)_(?P<prov>[A-Za-z]{2,4})$',

        # Pattern 3: Address City Province Project (space separators) - improved for multi-word addresses
        r'^(?P<addr>(?:[0-9]+\s+)?(?:[A-Za-z]+\s+)*[A-Za-z]+)\s+(?P<city>[A-Za-z][A-Za-z0-9]*?)\s+(?P<prov>[A-Za-z]{2,3})\s+(?P<project_name>.+)$',

        # Pattern 4: Address City Province (space separators, no project)
        r'^(?P<addr>(?:[0-9]+\s+)?(?:[A-Za-z]+\s+)*[A-Za-z]+)\s+(?P<city>[A-Za-z][A-Za-z0-9]*?)\s+(?P<prov>[A-Za-z]{2,3})$',

        # Pattern 5: Address-Project (dash separator for project only)
        r'^(?P<addr>.+?)-(?P<project_name>.+)$',

        # Pattern 6: Simple Address (no separators)
        r'^(?P<addr>.+)$'
    ]

    for pattern_str in patterns:
        pattern = re.compile(pattern_str)
        match = pattern.match(name_without_ext)

        if match:
            addr = match.group('addr') if 'addr' in match.groupdict() else None
            city = match.group('city') if 'city' in match.groupdict() else None
            prov = match.group('prov') if 'prov' in match.groupdict() else None
            project_name = match.group('project_name') if 'project_name' in match.groupdict() else None

            # Clean up address by replacing underscores with spaces
            if addr:
                addr = addr.replace('_', ' ').strip()

            # Clean up project name
            if project_name:
                project_name = project_name.strip()

            return addr, city, prov, project_name

    return None


def generate_nano_id(num):
    """
    Generate a unique identifier for the nano service.

    Args:
        num (int): The number of characters in the unique identifier.

    Returns:
        str: A unique identifier.
    """
    # Define the custom alphabet and length
    nanoKey = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
    return nanoid.generate(nanoKey, num)