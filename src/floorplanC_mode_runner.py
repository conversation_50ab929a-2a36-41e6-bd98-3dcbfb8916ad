import argparse
from datetime import datetime
import json
import os
import shutil
import sys
import uuid
import toml
from pathlib import Path
from src.batch_processing import generate_batch_file_from_pdf_list, process_and_check_batch
from src.floorplan_classification.image_wrangling import encode_image, extract_images_from_pdf, generate_nano_path_and_id, remove_file
from components_init import init_AI_config, init_mongo
from helpers.AIManager import AIManager
import logger
from src.utils import generate_nano_id, parse_name
from src.sequential_processing import process_pdfs_in_small_batches
from src.sequential_processing import process_pdfs_sequentially
from src.line_by_line_processing import process_jsonl_line_by_line


AI_NAME = 'openai'
AI_MODEL = ''


def handle_error(dir_path, error_img_path, error_message):
    """
    Moves the specified directory or file to the error path and logs the error message.

    Parameters:
    - dir_path: The path of the directory or file where the error occurred.
    - error_img_path: The destination path where the file will be moved.
    - error_message: The error message to be logged in the error.log.
    """

    try:
        # Create the error directory if it doesn't exist
        if not os.path.exists(error_img_path):
            os.makedirs(error_img_path)

        # Check if dir_path is a file or directory
        if os.path.isdir(dir_path):
            # Move directory
            destination_dir = os.path.join(
                error_img_path, os.path.basename(dir_path))
            shutil.move(dir_path, destination_dir)
            print(f"Moved directory {dir_path} to {destination_dir}")
            logger.get_logger().error(
                f"Moved directory {dir_path} to {destination_dir}")
        elif os.path.isfile(dir_path):
            # Move file
            destination_file = os.path.join(
                error_img_path, os.path.basename(dir_path))
            shutil.move(dir_path, destination_file)
            print(f"Moved file {dir_path} to {destination_file}")
            logger.get_logger().error(
                f"Moved file {dir_path} to {destination_file}")
        else:
            print(f"{dir_path} is neither a file nor a directory.")
            logger.get_logger().error(
                f"{dir_path} is neither a file nor a directory.")
            return

        # Define the path for error.log
        error_log_file = os.path.join(error_img_path, "error.log")

        # Write the error message into error.log
        with open(error_log_file, 'a') as log_file:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_file.write(
                f"[{timestamp}] Error with {dir_path}: {error_message}\n")
        logger.get_logger().debug(f"Error logged to {error_log_file}")

    except Exception as e:
        # Handle any unexpected errors that may occur during the move or logging process
        print(f"An error occurred while handling the error: {e}")
        logger.get_logger().error(
            f"An error occurred while handling the error: {e}")


def process_single_file(pdf_dict, file_name, file_path, output_folder, error_img_path):
    """
    Processes a single file by extracting images from it.

    Args:
        pdf_dict: The PDF dictionary to update.
        file_name: The name of the file.
        file_path: The full path to the file.
        output_folder: The output directory for extracted images.
        error_img_path: Path where errors are logged and problematic files are copied.

    Returns:
        Updated pdf_dict after processing the file.
    """
    file_path = os.path.abspath(file_path)
    pdf_dict['ai_name'] = AI_NAME
    pdf_dict['ai_model'] = AI_MODEL
    if file_name.lower().endswith(".pdf"):
        try:
            pdf_dict['all_files'].append(
                {"file_path": file_path, "file_type": "pdf"})

            pdf_dict = extract_images_from_pdf(
                file_path, output_folder, pdf_dict=pdf_dict)

        except Exception as e:
            error_message = f"Error processing extract images from pdf file {file_name}: {e}"
            print(error_message)
            logger.get_logger().error(error_message)
            error_img_path = os.path.join(error_img_path, file_name)
            handle_error(file_path, error_img_path, error_message)
    elif file_name.lower().endswith((".png", ".jpg", ".jpeg")):
        try:
            pdf_dict['all_files'].append(
                {"file_path": file_path, "file_type": "image"})
            output_filepath, saved_output_filepath = generate_nano_path_and_id(
                output_folder)
            shutil.copy(file_path, output_filepath)
            encode_str = encode_image(output_filepath)
            pdf_dict['images'].append(saved_output_filepath)
            pdf_dict['encoded_images'].append(encode_str)
        except Exception as e:
            error_message = f"Error encoding image {file_name}: {e}"
            print(error_message)
            logger.get_logger().error(error_message)
            error_img_path = os.path.join(error_img_path, file_name)
            handle_error(file_path, error_img_path, error_message)
    return pdf_dict


def extract_images_from_pdf_dir(pdf_dir, output_folder, error_img_path, mongo_client):
    """
    Main function to iterate through the provided directory and process directories and files.

    Parameters:
    - pdf_dir: The base directory containing files and directories.
    - output_folder: The output directory for extracted images.
    - error_img_path: Path where errors are logged and problematic files are copied.
    - mongo_client: The MongoDB client instance.

    Returns:
    - List of processed PDFs.
    """
    pdf_list = []

    if not os.path.exists(pdf_dir):
        print(f"Directory {pdf_dir} doesn't exist.")
        logger.get_logger().error(f"Directory {pdf_dir} doesn't exist.")
        sys.exit(1)

    os.makedirs(output_folder, exist_ok=True)

    # Walk through the directory
    dirs = os.listdir(pdf_dir)

    for dir_name in dirs:
        dir_path = os.path.join(pdf_dir, dir_name)

        if os.path.isdir(dir_path):
            print(f"--------Processing Directory: {dir_name}")
            logger.get_logger().debug(f"Processing Directory: {dir_name}")
            pdf_list = process_directory(
                dir_path, output_folder, error_img_path, pdf_list, mongo_client=mongo_client)
        else:
            print(f"--------Processing File: {dir_name}")
            logger.get_logger().debug(f"Processing File: {dir_name}")
            file_path = os.path.join(pdf_dir, dir_name)
            pdf_list = process_file_path(
                file_path, output_folder, error_img_path, pdf_list, mongo_client=mongo_client)

    return pdf_list


def process_directory(dir_path, output_folder, error_img_path, pdf_list, mongo_client):
    """
    Processes a directory by parsing its name and extracting images from files in it.

    Args:
        dir_path: Full path to the directory.
        output_folder: The output directory for extracted images.
        error_img_path: Path where errors are logged and problematic files are copied.
        pdf_list: List of PDFs that have been processed.
        mongo_client: The MongoDB client instance.

    Returns:
        Updated pdf_list after processing the directory.
    """
    dir_name = os.path.basename(dir_path)
    parsed_dir_info = parse_name(dir_name)
    pdf_dict = {"is_folder": True, "all_files": [], "_id": str(
        uuid.uuid4()), "images": [], "encoded_images": []}
    if parsed_dir_info:
        addr, city, prov, project_name = parsed_dir_info
        logger.get_logger().debug(
            f"Parsed Directory: Addr: {addr}, City: {city}, Prov: {prov}, Project Name: {project_name}")
        pdf_dict = check_and_set_addr_city_prov_project_name(
            pdf_dict, addr, city, prov, project_name, mongo_client)
        if pdf_dict is None:  # Skip processing if the AI model and name are the same
            logger.get_logger().debug('AI model and name are the same. Skipping processing.')
            return pdf_list
        files = os.listdir(dir_path)
        for file in files:
            pdf_dict = process_single_file(pdf_dict, file, os.path.join(
                dir_path, file), output_folder, error_img_path)
        pdf_list.append(pdf_dict)
    else:
        error_message = f"Directory {dir_name} doesn't match the expected pattern."
        logger.get_logger().error(error_message)
        print(error_message)
        handle_error(dir_path, error_img_path, error_message)

    return pdf_list


def check_and_set_addr_city_prov_project_name(pdf_dict, addr, city, prov, project_name, mongo_client):
    """
    Check if the AI model and name are the same
    if the address, city, province, and project name already exist in the MongoDB database, update the pdf_dict with the existing ID.

    Args:
        pdf_dict: The PDF dictionary to update.
        addr: The address of the project.
        city: The city of the project.
        prov: The province of the project.
        project_name: The name of the project.
        mongo_client: The MongoDB client instance.

    Returns:
        Updated pdf_dict with the existing ID if it exists.
    """
    pdf_dict["addr"] = addr
    pdf_dict["city"] = city
    pdf_dict["prov"] = prov
    pdf_dict["project_name"] = project_name
    exist_floorplan = mongo_client.findOne(query={
        'addr': addr,
        'city': city,
        'prov': prov,
        'project_name': project_name
    })
    if exist_floorplan:
        pdf_dict['_id'] = exist_floorplan['_id']
    if exist_floorplan and 'ai_results' in exist_floorplan and exist_floorplan['ai_model'] == AI_MODEL and exist_floorplan['ai_name'] == AI_NAME:
        # Skip processing if the AI model and name are the same
        logger.get_logger().debug('AI model and name are the same. Skipping processing.')
        return None
    return pdf_dict


def process_file_path(file_path, output_folder, error_img_path, pdf_list, mongo_client):
    """
    Processes a file by parsing its name and extracting images if it's a PDF.

    Args:
        file_path: Full path to the file.

        output_folder: The output directory for extracted images.
        error_img_path: Path where errors are logged and problematic files are copied.
        pdf_list: List of PDFs that have been processed.
        mongo_client: The MongoDB client instance.

    Returns:
        Updated pdf_list after processing the file.
    """
    file_name = os.path.basename(file_path)
    filename = Path(file_name).stem
    parsed_file_info = parse_name(filename)
    pdf_dict = {"is_folder": False, "all_files": [], "_id": str(
        uuid.uuid4()), "images": [], "encoded_images": []}
    if parsed_file_info:
        addr, city, prov, project_name = parsed_file_info
        logger.get_logger().debug(
            f"Parsed File: Addr: {addr}, City: {city}, Prov: {prov}, Project Name: {project_name}")
        pdf_dict = check_and_set_addr_city_prov_project_name(
            pdf_dict, addr, city, prov, project_name, mongo_client)
        if pdf_dict is None:  # Skip processing if the AI model and name are the same
            return pdf_list
        pdf_dict = process_single_file(pdf_dict, file_name, file_path,
                                       output_folder, error_img_path)
        pdf_list.append(pdf_dict)
    else:
        error_message = f"File {file_name} doesn't match the expected pattern."
        logger.get_logger().error(error_message)
        print(error_message)
        handle_error(file_path, error_img_path, error_message)

    return pdf_list


def store_pdf_list_in_mongo(mongo_client, pdf_list):
    """
    Store the list of PDF dictionaries in MongoDB.

    Args:
        mongo_client (MongoDBClient): The MongoDB client instance.
        pdf_list (list): List of PDF dictionaries to store in MongoDB.

    Returns:
        None
    """
    cleaned_pdf_list = []
    for pdf in pdf_list:
        # Create a copy of the original dict to avoid modifying the original list
        pdf_copy = pdf.copy()
        if 'encoded_images' in pdf_copy:
            del pdf_copy['encoded_images']  # Remove the 'encoded_images' field
        cleaned_pdf_list.append(pdf_copy)

    print(f"Cleaned PDF List: {cleaned_pdf_list}")
    # Insert the cleaned PDF dict into MongoDB
    try:
        mongo_client.write_list(cleaned_pdf_list)
    except Exception as e:
        print(f"Error writing to MongoDB: {e}")
        logger.get_logger().error(f"Error writing to MongoDB: {e}")


def combine_units(units):
    """
    Combine units with the same level and face into a single entry.

    Args:
        units (list): List of unit dictionaries.

    Returns:
        list: List of combined unit dictionaries.
    """
    combined = {}

    # Iterate through each unit
    for unit in units:
        key = (unit["lvl"], unit["fce"])

        if key in combined:
            combined[key]["unt"].append(str(unit["unt"]))  # Convert to string
        else:
            combined[key] = {"unt": [str(unit["unt"])],  # Convert to string
                             "lvl": unit["lvl"], "fce": unit["fce"]}

    # Convert the combined units back into the desired format
    result = []
    for value in combined.values():
        # Join the unt list into a single string (comma-separated)
        value["unt"] = ",".join(value["unt"])
        result.append(value)

    return result


def generate_result_json(mongo_client, result_file, pdf_list):
    """
    Generate the result JSON from the MongoDB client.

    Args:
        mongo_client (MongoDBClient): The MongoDB client instance.
        result_file (str): The path to the result file.

    Returns: 
    bool: True if the result JSON was generated successfully, False otherwise.
    """
    try:
        if not pdf_list:
            result_list = mongo_client.find()
            # print(result_list)
        else:
            result_list = []
            for pdf in pdf_list:
                result = mongo_client.findOne(query={'_id': pdf['_id']})
                result_list.append(result)

        result_json = []

        for doc in result_list:
            if 'images' not in doc or 'ai_results' not in doc:
                logger.get_logger().warning(f"Missing required fields for document {doc.get('_id', 'unknown')}")
                continue
            if len(doc['images']) != len(doc['ai_results']):
                logger.get_logger().warning(
                    f"Number of images and AI results do not match for {doc['_id']}")
                continue
            new_doc = {}
            new_doc['sid'] = doc['_id']
            new_doc['addr'] = doc['addr']
            new_doc['city'] = doc['city']
            new_doc['prov'] = doc['prov']
            new_doc['nm'] = doc['project_name']
            new_doc['source'] = doc['all_files']
            new_doc['src'] = 'floorplanC'
            new_doc['floorplan'] = []
            # print(len(doc['images']),doc['_id'])
            for img_index in range(len(doc['images'])):
                ai_result = doc['ai_results'][img_index]
                image = doc['images'][img_index]
                # Combine units with the same level and face
                combined_units = combine_units(ai_result['unts'])
                if not combined_units:
                    logger.get_logger().warning(
                        f"No detail information got from AI for {doc['_id']}, {image}, check the image please.")
                    continue
                for unit in combined_units:
                    new_floorplan = {}
                    new_floorplan['nm'] = ai_result.get('nm', '')
                    new_floorplan['unit_no'] = str(
                        unit['unt'])  # Ensure unit_no is string
                    new_floorplan['bdrms'] = ai_result.get('bdrms', 0)
                    new_floorplan['bthrms'] = ai_result.get('bthrms', 0)
                    new_floorplan['img'] = image
                    new_floorplan['fce'] = [unit['fce']]
                    new_floorplan['lvl'] = [unit['lvl']]
                    new_floorplan['sqft'] = ai_result.get('sqft', 0)
                    new_floorplan['id'] = generate_nano_id(16)
                    new_doc['floorplan'].append(new_floorplan)
            result_json.append(new_doc)
        with open(result_file, 'w') as file:
            result_json = json.dumps(result_json, default=str, indent=4)
            file.write(result_json + '\n')
        logger.get_logger().info(
            f"Result JSON generated successfully: {result_file}")
    except Exception as e:
        logger.get_logger().error(f"Error generating result JSON: {e}")
        return False
    return True


def process_batch_mode(batch_mode, prompt_openAI, pdf_list, manager, api_key, mongo_client, small_batch_size=3):
    """
    Process PDFs using different batch processing modes.

    Args:
        batch_mode (str): The batch processing mode ('sequential', 'line_by_line', 'small_batch', 'large_batch')
        prompt_openAI (str): The AI prompt for processing
        pdf_list (list): List of PDF dictionaries to process
        manager (AIManager): The AI manager instance
        api_key (str): OpenAI API key
        mongo_client: MongoDB client instance
        small_batch_size (int): Size for small batch processing mode

    Returns:
        None
    """
    if batch_mode == 'sequential':
        # Sequential processing mode
        logger.get_logger().info("Using sequential processing mode")
        process_pdfs_sequentially(prompt_openAI, pdf_list, manager, api_key, mongo_client)

    elif batch_mode == 'line_by_line':
        # Total jsonl + line by line processing mode
        logger.get_logger().info(
            "Using line-by-line processing mode (master JSONL + individual processing)")
        # Generate master JSONL for line-by-line processing
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        master_jsonl_path = f'logs/openai_batch_{timestamp}.jsonl'
        generate_batch_file_from_pdf_list(prompt_openAI, pdf_list, master_jsonl_path, manager)

        process_jsonl_line_by_line(master_jsonl_path, api_key, mongo_client, pdf_list)

    elif batch_mode == 'small_batch':
        # Small batch processing mode
        logger.get_logger().info(
            f"Using small batch processing mode (batch size: {small_batch_size})")
        process_pdfs_in_small_batches(prompt_openAI, pdf_list, manager,
                                      api_key, mongo_client, batch_size=small_batch_size)

    elif batch_mode == 'large_batch':
        # Original large batch processing mode (may hit token limit)
        logger.get_logger().info("Using large batch processing mode (original method)")
        logger.get_logger().warning("Large batch mode may hit token limits with many PDFs")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        jsonl_path = f'logs/openai_batch_{timestamp}.jsonl'
        generate_batch_file_from_pdf_list(prompt_openAI, pdf_list, jsonl_path, manager)
        process_and_check_batch(api_key, jsonl_path, mongo_client, pdf_list)

    else:
        # Default use sequential processing
        logger.get_logger().warning(
            f"Unknown batch mode '{batch_mode}', falling back to sequential processing")
        process_pdfs_sequentially(prompt_openAI, pdf_list, manager, api_key, mongo_client)


def setup_master_jsonl(prompt_openAI, pdf_list, manager):
    """
    Generate master JSONL file for batch processing.

    Args:
        prompt_openAI (str): The AI prompt for processing
        pdf_list (list): List of PDF dictionaries to process
        manager (AIManager): The AI manager instance

    Returns:
        str: Path to the generated master JSONL file
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    master_jsonl_path = f'logs/openai_batch_{timestamp}.jsonl'
    pdf_count = len(pdf_list)

    logger.get_logger().info(f"Generating master JSONL file: {master_jsonl_path}")
    generate_batch_file_from_pdf_list(prompt_openAI, pdf_list, master_jsonl_path, manager)
    logger.get_logger().info(f"Master JSONL file generated with {pdf_count} entries")

    return master_jsonl_path


def cleanup_master_jsonl(master_jsonl_path, keep_master_jsonl, pdf_count):
    """
    Clean up or preserve the master JSONL file based on configuration.

    Args:
        master_jsonl_path (str): Path to the master JSONL file
        keep_master_jsonl (bool): Whether to keep the master JSONL file
        pdf_count (int): Number of PDFs processed

    Returns:
        None
    """
    if not keep_master_jsonl:
        try:
            remove_file(master_jsonl_path)
            logger.get_logger().info(f"JSONL batch file deleted: {master_jsonl_path}")
        except Exception as e:
            logger.get_logger().warning(
                f"Failed to delete JSONL batch file {master_jsonl_path}: {e}")
    else:
        logger.get_logger().info(f"JSONL batch file preserved: {master_jsonl_path}")
        logger.get_logger().info(
            f"File contains {pdf_count} batch entries for future reference")


def get_ai_prompt():
    """
    Get the standardized AI prompt for floorplan analysis.

    Returns:
        str: The AI prompt text
    """
    return """You are a real estate expert analyzing floorplan images. For each provided image, extract the following details and return them in a JSON array format.

IMPORTANT: You must return a JSON ARRAY (list) containing one object per image. The response should be a list, not a single object.

For each image, extract:
- nm: Project/building name or unit name (e.g., "CRIMSON", "T1", "1B+D")
- bdrms: Number of bedrooms (0 if none labeled)
- bthrms: Number of bathrooms (0 if none labeled)
- sqft: Interior square footage (excluding balcony)
- unts: Array of unit objects, each containing:
  - unt: Unit number (string, e.g., "5" not "05")
  - lvl: Floor range (string, e.g., "5-36" or "6")
  - fce: Orientation (N, NE, E, SE, S, SW, W, NW)

Example response format:
[{"nm": "CRIMSON", "bdrms": 2, "bthrms": 2, "sqft": 1870, "unts": [{"unt": "5", "lvl": "5-36", "fce": "NE"}, {"unt": "7", "lvl": "6-10", "fce": "N"}]}, {"nm": "T1", "bdrms": 1, "bthrms": 1, "sqft": 850, "unts": [{"unt": "3", "lvl": "3", "fce": "E"}]}]

Rules:
1. Return exactly one object per input image
2. If image is unreadable, return: {"nm": "", "bdrms": 0, "bthrms": 0, "sqft": 0, "unts": []}
3. Use 0 for missing numbers, empty string "" for missing text
4. Always include all fields
5. Do not guess - only use visible information
6. The response must be a valid JSON array
7. Return only the JSON array, no explanations or comments"""


def run_flc_mode(config, only_get_json=False):
    """
    Run the floorplanC mode to process floorplan images and extract details using AI.

    Args:
        config (dict): The configuration dictionary.
        only_get_json (bool): Flag to only generate the JSON output.

    Returns:
        None
    """
    print("Running floorplanC mode..., only_get_json: ", only_get_json)

    # Initialize AI and database components
    AI_config = init_AI_config(config)
    manager = AIManager(AI_config)
    global AI_NAME, AI_MODEL
    AI_NAME = config['AI']['name']
    AI_MODEL = config['AI']['model']
    logger.get_logger().debug(f"AI Name: {AI_NAME}, AI Model: {AI_MODEL}")

    result_file_path = config['pdfImageOutput']['result_file']
    mongo_client = init_mongo(config, fpc=True)

    # Handle JSON-only mode
    if only_get_json:
        logger.get_logger().info("Only generating the JSON output. Skipping batch processing.")
        generate_result_json(mongo_client, result_file_path, [])
        return

    # Extract and process PDF images
    pdf_list = extract_images_from_pdf_dir(
        config['pdfSource']['local_path'],
        config['pdfImageOutput']['img_path'],
        config['pdfImageOutput']['error_img_path'],
        mongo_client=mongo_client
    )

    if not pdf_list:
        logger.get_logger().info("No new PDFs found. Exiting...")
        return

    store_pdf_list_in_mongo(mongo_client, pdf_list)

    # Get configuration parameters
    batch_mode = config['openAI'].get('batch_mode', 'sequential')
    small_batch_size = int(config['openAI'].get('small_batch_size', 3))
    api_key = config['openAI']['key']

    # Handle keep_master_jsonl configuration
    keep_master_jsonl_value = config['openAI'].get('keep_master_jsonl', 'true')
    if isinstance(keep_master_jsonl_value, bool):
        keep_master_jsonl = keep_master_jsonl_value
    else:
        keep_master_jsonl = str(keep_master_jsonl_value).lower() == 'true'

    pdf_count = len(pdf_list)
    logger.get_logger().info(f"Found {pdf_count} PDFs to process using {batch_mode} mode")

    # Get AI prompt and setup master JSONL
    prompt_openAI = get_ai_prompt()
    master_jsonl_path = setup_master_jsonl(prompt_openAI, pdf_list, manager)

    # Log master JSONL handling strategy
    if keep_master_jsonl:
        logger.get_logger().info(f"Master JSONL file will be preserved: {master_jsonl_path}")
    else:
        logger.get_logger().info("Master JSONL file will be deleted after processing")

    # Process PDFs using the specified batch mode
    process_batch_mode(
        batch_mode=batch_mode,
        prompt_openAI=prompt_openAI,
        pdf_list=pdf_list,
        manager=manager,
        api_key=api_key,
        mongo_client=mongo_client,
        small_batch_size=small_batch_size
    )

    # Generate final result JSON
    generate_result_json(mongo_client, result_file_path, pdf_list)

    # Clean up or preserve master JSONL file
    cleanup_master_jsonl(master_jsonl_path, keep_master_jsonl, pdf_count)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="floorplanC runner script")
    parser.add_argument(
        '-c', '--config',
        default='config/config.ini',
        help='Path to the configuration file (default: config/config.ini)'
    )
    parser.add_argument(
        '--only_get_json',
        action='store_true',
        help='Only run the script to get the JSON output (default: False)'
    )
    args = parser.parse_args()
    config_all = toml.load(args.config)
    config = config_all['pyfpimg']
    run_flc_mode(config, args.only_get_json)
