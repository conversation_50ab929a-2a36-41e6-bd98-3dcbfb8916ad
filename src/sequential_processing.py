import json
import time
import base64
import io
from math import ceil
from PIL import Image
import logger as logger
from helpers.AIManager import <PERSON>Manager
from src.floorplan_classification.image_wrangling import remove_file
from src.batch_processing import process_and_check_batch
from src.helpers.openAI_batch import (
    check_batch_status, create_batch, create_client,
    retrieve_batch_results, upload_batch_input_file
)


def count_image_tokens(width, height):
    """
    Calculate image tokens based on OpenAI Vision API official algorithm.

    The algorithm:
    1. Scale image to fit within 1024×1024
    2. Cover image with 512×512 tiles
    3. Base cost: 85 tokens + 170 tokens per tile

    Args:
        width (int): Image width in pixels
        height (int): Image height in pixels

    Returns:
        int: Number of tokens required for the image
    """
    # Scale image to fit within 1024×1024 if necessary
    if width > 1024 or height > 1024:
        if width > height:
            height = int(height * 1024 / width)
            width = 1024
        else:
            width = int(width * 1024 / height)
            height = 1024

    # Calculate number of 512×512 tiles needed
    h = ceil(height / 512)
    w = ceil(width / 512)

    # Base cost + tile cost
    return 85 + 170 * h * w


def get_image_dimensions_from_base64(encoded_image):
    """
    Extract image dimensions from base64 encoded image data.

    Args:
        encoded_image (str): Base64 encoded image data

    Returns:
        tuple: (width, height) or (None, None) if extraction fails
    """
    try:
        # Decode base64 to bytes
        image_data = base64.b64decode(encoded_image)

        # Create PIL Image from bytes
        image = Image.open(io.BytesIO(image_data))

        return image.size  # Returns (width, height)
    except Exception as e:
        logger.get_logger().error(f"Failed to extract image dimensions: {e}")
        return None, None


def estimate_tokens_for_pdf(pdf_dict):
    """
    Estimate the number of tokens required to process a single PDF using OpenAI Vision API official algorithm.

    Args:
        pdf_dict (dict): PDF dictionary containing encoded images

    Returns:
        int: Estimated number of tokens
    """
    # Base tokens (prompts, etc.)
    base_tokens = 1000

    # Calculate tokens for each image using official OpenAI Vision API algorithm
    image_tokens = 0
    if 'encoded_images' in pdf_dict:
        for encoded_image in pdf_dict['encoded_images']:
            if not encoded_image:
                continue

            # Get actual image dimensions from base64 data
            width, height = get_image_dimensions_from_base64(encoded_image)

            if width and height:
                # Use official OpenAI Vision API token calculation
                estimated_tokens = count_image_tokens(width, height)
                logger.get_logger().debug(
                    f"Image {width}x{height} pixels requires {estimated_tokens} tokens")
            else:
                # Fallback to conservative estimate if dimensions cannot be extracted
                estimated_tokens = 1000
                logger.get_logger().warning(
                    "Could not extract image dimensions, using fallback estimate of 1000 tokens")

            image_tokens += estimated_tokens

    total_tokens = base_tokens + image_tokens
    logger.get_logger().debug(
        f"Estimated tokens for {pdf_dict.get('_id', 'unknown')}: {total_tokens} "
        f"(base: {base_tokens}, images: {image_tokens})")
    return total_tokens


def create_single_batch_file(prompt: str, pdf_dict: dict, batch_file: str, manager: AIManager):
    """
    Create a batch file for a single PDF

    Args:
        prompt (str): Prompt text
        pdf_dict (dict): Single PDF data dictionary
        batch_file (str): Batch file path
        manager (AIManager): AI manager instance
    """
    remove_file(batch_file)
    try:
        manager.generate_batch_file(prompt, pdf_dict, batch_file, 'openai')
        logger.get_logger().info(
            f"Single batch file created for {pdf_dict.get('_id', 'unknown')}: {batch_file}")
    except ValueError as e:
        error_message = f"Error generating batch file for {pdf_dict.get('pdf_name', 'unknown')}: {e}"
        logger.get_logger().error(error_message)
        raise


def process_single_batch(api_key, batch_file, mongo_client, pdf_dict):
    """
    Process a single batch job

    Args:
        api_key (str): OpenAI API key
        batch_file (str): Batch file path
        mongo_client: MongoDB client
        pdf_dict (dict): PDF data dictionary

    Returns:
        bool: Whether processing was successful
    """
    try:
        create_client(api_key)

        # Upload batch file
        logger.get_logger().info(
            f"Uploading batch file for {pdf_dict.get('_id', 'unknown')}")
        batch_input_file_id = upload_batch_input_file(batch_file)
        if not batch_input_file_id:
            raise Exception(f"Failed to upload batch file {batch_file}")

        # Create batch job
        batch_id = create_batch(
            batch_input_file_id,
            description=f"single batch for {pdf_dict.get('_id', 'unknown')}"
        )
        if not batch_id:
            raise Exception(f"Failed to create batch job for {batch_file}")

        logger.get_logger().info(
            f"Batch job {batch_id} created for {pdf_dict.get('_id', 'unknown')}")

        # Wait for batch completion
        max_wait_time = 30 * 60  # 30 minutes maximum wait time
        wait_interval = 30  # Check every 30 seconds
        total_waited = 0

        while total_waited < max_wait_time:
            batch_status = check_batch_status(batch_id)
            logger.get_logger().info(
                f"Batch {batch_id} status: {batch_status.status}")

            if batch_status.status == 'completed':
                if not batch_status.output_file_id:
                    raise Exception(
                        f"Batch {batch_id} completed but no output file available")

                # Process results
                results = retrieve_batch_results(batch_status.output_file_id)
                logger.get_logger().info(
                    f"Processing results for batch {batch_id}")

                # Parse and save results
                result_list = results.split('\n')
                for result in result_list:
                    if result.strip():
                        result_json = json.loads(result)
                        custom_id = result_json['custom_id']
                        ai_result_str = result_json['response']['body']['choices'][0]['message']['content']
                        ai_result_str = ai_result_str.replace('\n', '').replace(
                            '\r', '').replace('```json', '').replace('```', '').strip()

                        logger.get_logger().debug(
                            f"AI Result for {custom_id}: {ai_result_str}")
                        ai_result = json.loads(ai_result_str)

                        # Ensure ai_result is always a list (array)
                        # Since one PDF can contain multiple images, we expect an array
                        if isinstance(ai_result, dict):
                            # If AI returned a single object instead of array, wrap it in a list
                            ai_result = [ai_result]
                            logger.get_logger().warning(
                                f"AI returned single object for {custom_id}, wrapped in array")
                        elif isinstance(ai_result, list):
                            # Already a list, this is expected
                            # Check if the number of returned items matches the number of input images
                            returned_count = len(ai_result)
                            expected_count = len(
                                pdf_dict.get('encoded_images', []))

                            if returned_count == expected_count:
                                logger.get_logger().debug(
                                    f"AI returned array with {returned_count} items for {custom_id} (matches input)")
                            else:
                                logger.get_logger().warning(
                                    f"AI returned array with {returned_count} items for {custom_id}, but expected {expected_count} (input images count mismatch)")
                        else:
                            # Unexpected format
                            logger.get_logger().error(
                                f"Unexpected AI result type for {custom_id}: {type(ai_result)}")
                            continue

                        mongo_client.update_ai_result(custom_id, ai_result)

                logger.get_logger().info(
                    f"Successfully processed batch {batch_id}")
                return True

            elif batch_status.status == 'failed':
                error_msg = f"Batch {batch_id} failed"
                if hasattr(batch_status, 'errors') and batch_status.errors:
                    error_msg += f": {batch_status.errors}"
                raise Exception(error_msg)

            elif batch_status.status in ['expired', 'cancelled']:
                raise Exception(f"Batch {batch_id} was {batch_status.status}")

            # Wait for next check
            time.sleep(wait_interval)
            total_waited += wait_interval

        # Timeout
        raise Exception(
            f"Batch {batch_id} timed out after {max_wait_time} seconds")

    except Exception as e:
        logger.get_logger().error(
            f"Error processing single batch for {pdf_dict.get('_id', 'unknown')}: {e}")
        raise


def process_pdfs_sequentially(prompt: str, pdf_list: list, manager: AIManager, api_key: str, mongo_client):
    """
    Process PDF list one by one to avoid token quota limits

    Args:
        prompt (str): Prompt text
        pdf_list (list): PDF list
        manager (AIManager): AI manager instance
        api_key (str): OpenAI API key
        mongo_client: MongoDB client
    """
    total_pdfs = len(pdf_list)
    processed_count = 0
    failed_count = 0

    logger.get_logger().info(
        f"Starting sequential processing of {total_pdfs} PDFs")

    for i, pdf_dict in enumerate(pdf_list, 1):
        pdf_id = pdf_dict.get('_id', f'pdf_{i}')
        logger.get_logger().info(f"Processing PDF {i}/{total_pdfs}: {pdf_id}")

        try:
            # Estimate token count
            estimated_tokens = estimate_tokens_for_pdf(pdf_dict)
            logger.get_logger().info(
                f"Estimated tokens for {pdf_id}: {estimated_tokens}")

            # Skip if single PDF token count exceeds limit
            # if estimated_tokens > 85000:  # Leave some margin
            #     logger.get_logger().warning(f"PDF {pdf_id} estimated tokens ({estimated_tokens}) too large, skipping")
            #     failed_count += 1
            #     continue

            # Create single batch file
            timestamp = int(time.time())
            batch_file = f'logs/single_batch_{pdf_id}_{timestamp}.jsonl'

            create_single_batch_file(prompt, pdf_dict, batch_file, manager)

            # Process single batch
            success = process_single_batch(
                api_key, batch_file, mongo_client, pdf_dict)

            if success:
                processed_count += 1
                logger.get_logger().info(
                    f"Successfully processed {pdf_id} ({processed_count}/{total_pdfs})")
            else:
                failed_count += 1
                logger.get_logger().error(f"Failed to process {pdf_id}")

            # Clean up temporary files
            remove_file(batch_file)

            # Add brief delay between processing to avoid API limits
            if i < total_pdfs:  # Not the last one
                logger.get_logger().info("Waiting 5 seconds before next batch...")
                time.sleep(5)

        except Exception as e:
            failed_count += 1
            logger.get_logger().error(f"Error processing PDF {pdf_id}: {e}")
            continue

    # Processing completion summary
    logger.get_logger().info("Sequential processing completed:")
    logger.get_logger().info(f"  Total PDFs: {total_pdfs}")
    logger.get_logger().info(f"  Successfully processed: {processed_count}")
    logger.get_logger().info(f"  Failed: {failed_count}")
    logger.get_logger().info(
        f"  Success rate: {processed_count/total_pdfs*100:.1f}%")


def process_pdfs_in_small_batches(prompt: str, pdf_list: list, manager: AIManager, api_key: str, mongo_client, batch_size=5):
    """
    Divide PDF list into small batches for processing, keeping each batch within token limits

    Args:
        prompt (str): Prompt text
        pdf_list (list): PDF list
        manager (AIManager): AI manager instance
        api_key (str): OpenAI API key
        mongo_client: MongoDB client
        batch_size (int): Number of PDFs per batch
    """
    total_pdfs = len(pdf_list)
    logger.get_logger().info(
        f"Starting small batch processing of {total_pdfs} PDFs with batch size {batch_size}")

    # Divide PDF list into small batches
    for batch_start in range(0, total_pdfs, batch_size):
        batch_end = min(batch_start + batch_size, total_pdfs)
        current_batch = pdf_list[batch_start:batch_end]
        batch_num = (batch_start // batch_size) + 1
        total_batches = (total_pdfs + batch_size - 1) // batch_size

        logger.get_logger().info(
            f"Processing batch {batch_num}/{total_batches} (PDFs {batch_start+1}-{batch_end})")

        # Estimate total tokens for current batch
        total_estimated_tokens = sum(
            estimate_tokens_for_pdf(pdf) for pdf in current_batch)
        logger.get_logger().info(
            f"Batch {batch_num} estimated tokens: {total_estimated_tokens}")

        # If estimated tokens exceed limit, process individually
        if total_estimated_tokens > 80000:  # Leave some margin
            logger.get_logger().warning(
                f"Batch {batch_num} tokens too large, processing individually")
            process_pdfs_sequentially(
                prompt, current_batch, manager, api_key, mongo_client)
        else:
            # Use original batch processing logic
            try:
                timestamp = int(time.time())
                batch_file = f'logs/small_batch_{batch_num}_{timestamp}.jsonl'

                # Generate batch file
                remove_file(batch_file)
                for pdf_dict in current_batch:
                    manager.generate_batch_file(
                        prompt, pdf_dict, batch_file, 'openai')

                logger.get_logger().info(
                    f"Small batch file created: {batch_file}")

                # Process batch
                process_and_check_batch(api_key, batch_file, mongo_client)

                logger.get_logger().info(
                    f"Successfully processed batch {batch_num}")

                # Clean up files
                remove_file(batch_file)

            except Exception as e:
                logger.get_logger().error(
                    f"Error processing batch {batch_num}: {e}")
                # If batch processing fails, fall back to individual processing
                logger.get_logger().info(
                    f"Falling back to sequential processing for batch {batch_num}")
                process_pdfs_sequentially(
                    prompt, current_batch, manager, api_key, mongo_client)

        # Delay between batches
        if batch_end < total_pdfs:
            logger.get_logger().info("Waiting 10 seconds before next batch...")
            time.sleep(10)
