import glob
import os
import re
from helpers.MongoDBClient import MongoDBClient
from helpers.KafkaProducer import KafkaProducer
from helpers.KafkaConsumer import KafkaConsumer
from floorplan_classification.model import ModelManager
import logger

def init_kafka_producer(config, test_mode=False):
    """
    Initialize Kafka producer.

    Args:
        config (ConfigParser): Configuration object.
        test_mode (bool, optional): If True, uses the test topic name. Defaults to False.

    Returns:
        kafka_producer: Kafka producer instance.

    Raises:
        Exception: If there's an error during configuration.
    """
    try:
        kafka_response_bootstrap_servers = config['kafka']['response']['bootstrap_servers']
        # Determine kafka_response_topic_name based on test_mode
        kafka_response_topic_name = config['kafka']['response'][
            '_test_topic_name'] if test_mode else config['kafka']['response']['topic_name']

        logger.get_logger().debug("Kafka response bootstrap servers: %s",
                                  kafka_response_bootstrap_servers)
        logger.get_logger().debug("Kafka response topic name: %s", kafka_response_topic_name)
        kafka_producer = KafkaProducer(
            kafka_response_bootstrap_servers, kafka_response_topic_name)

        return kafka_producer
    except Exception as e:
        error_msg = f"Error configuring kafka producer: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e


def init_kafka_consumer(config, test_mode=False):
    """
    Initialize Kafka consumer.

    Args:
        config (ConfigParser): Configuration object.
        test_mode (bool, optional): If True, uses the test topic name. Defaults to False.

    Returns:
        kafka_consumer: Kafka consumer instance.

    Raises:
        Exception: If there's an error during configuration.
    """
    try:
        kafka_request_bootstrap_servers = config['kafka']['request']['bootstrap_servers']
        # Determine kafka_request_topic_init and kafka_request_topic_routine based on test_mode
        kafka_request_topic_init = config['kafka']['request'][
            '_test_topic_init'] if test_mode else config['kafka']['request']['topic_init']
        kafka_request_topic_routine = config['kafka']['request'][
            '_test_topic_routine'] if test_mode else config['kafka']['request']['topic_routine']
        kafka_request_auto_offset_reset = config['kafka']['request']['auto_offset_reset']
        kafka_request_group_id = config['kafka']['request']['group_id']
        kafka_request_timeout = int(config['kafka']['request']['timeout'])

        logger.get_logger().debug("Kafka request bootstrap servers: %s",
                                  kafka_request_bootstrap_servers)
        logger.get_logger().debug("Kafka request init topic name: %s", kafka_request_topic_init)
        logger.get_logger().debug("Kafka request routine topic name: %s",
                                  kafka_request_topic_routine)
        logger.get_logger().debug("Kafka request auto_offset_reset: %s",
                                  kafka_request_auto_offset_reset)
        logger.get_logger().debug("Kafka request group_id: %s", kafka_request_group_id)
        logger.get_logger().debug("Kafka request timeout(S): %d", kafka_request_timeout)

        kafka_consumer_init = KafkaConsumer(
            kafka_request_bootstrap_servers, kafka_request_group_id, kafka_request_auto_offset_reset, kafka_request_topic_init, float(
                kafka_request_timeout)
        )
        kafka_consumer_routine = KafkaConsumer(
            kafka_request_bootstrap_servers, kafka_request_group_id, kafka_request_auto_offset_reset, kafka_request_topic_routine, float(
                kafka_request_timeout)
        )

        return kafka_consumer_init, kafka_consumer_routine
    except Exception as e:
        error_msg = f"Error configuring kafka consumer: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e


def init_model_manager(config):
    """
    Initialize model manager.

    Args:
        config (ConfigParser): Configuration object.

    Returns:
        model_manager: Model manager instance.

    Raises:
        Exception: If there's an error during configuration.
    """
    try:
        model_path = config['model']['path']
        logger.get_logger().debug("Model Path: %s", model_path)

        if 'name' in config['model']:
            model_name = config['model']['name']
            model_path = os.path.join(model_path, model_name)
        else:
            model_path = auto_get_model_path(model_path)

        logger.get_logger().debug("Model Name: %s", model_path)
        class_name_file = os.path.join(
            'src', 'floorplan_classification', 'class_names.txt')
        model_manager = ModelManager(model_path, class_name_file)

        return model_manager
    except Exception as e:
        error_msg = f"Error configuring model manager: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e


def auto_get_model_path(model_directory, get_newest=False):
    """
    Get the path of the newest or the model with the highest index in a directory.

    Args:
        model_directory (str): The directory containing the model files.
        get_newest (bool, optional): If True, retrieves the newest model. 
            If False, retrieves the model with the highest index. Defaults to True.

    Returns:
        str: The path to the selected model file.
    """
    list_of_files = glob.glob(os.path.join(model_directory, '*.keras'))

    if not list_of_files:
        raise FileNotFoundError(
            f"No Keras models found in directory {model_directory}")

    if get_newest:
        newest_file = max(list_of_files, key=os.path.getctime)
        return newest_file

    indices = [int(re.search(r'_(\d+)_', file).group(1))
               for file in list_of_files]

    highest_f1 = max(indices)

    highest_model = next(
        file for file in list_of_files if f'_{highest_f1}_' in file)

    return highest_model


def init_mongo(config, logger=logger, fpc=False):
    """
    Initialize MongoDB client and URI.

    Args:
        config (ConfigParser): Configuration object.
        logger (Logger, optional): Logger instance. Defaults to logger.
        fpc (bool, optional): If True, uses the FLC MongoDB configuration. Defaults to False.

    Returns:
        mongo_client: MongoDB client instance.

    Raises:
        Exception: If there's an error during configuration.
    """
    if fpc:
        mongo_config = config['mongoFLC']
    else:
        mongo_config = config['mongo']
    try:
        mongo_user = mongo_config['user']
        mongo_password = mongo_config['password']
        mongo_host = mongo_config['host']
        mongo_port = mongo_config['port']
        mongo_db = mongo_config['name']
        mongo_collection = mongo_config['collection']
        mongo_tlsCAFile = mongo_config['tlsCAFile']
        mongo_uri = f"mongodb://{mongo_user}:{mongo_password}@{mongo_host}:{mongo_port}/{mongo_db}?authSource=admin&retryWrites=true&w=majority&tls=true&tlsCAFile={mongo_tlsCAFile}"
        # mongo_uri = f"mongodb://{mongo_user}:{mongo_password}@{mongo_host}:{mongo_port}/{mongo_db}?authSource=admin&retryWrites=true&w=majority"

        logger.get_logger().debug("MongoDB URI: %s", mongo_uri)
        mongo_client = MongoDBClient(mongo_uri, mongo_db, mongo_collection)

        return mongo_client
    except Exception as e:
        error_msg = f"Error configuring mongo: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e


def init_image_source(config):
    """
    Initialize image source.

    Args:
        config (ConfigParser): Configuration object.

    Returns:
        source_type: Image source type.
        img_source: Image source.
    """
    try:
        local_paths = {
            'trb': config.get('imageSource', {}).get('local_path_trb'),
            'orb': config.get('imageSource', {}).get('local_path_orb'),
            'cda': config.get('imageSource', {}).get('local_path_cda'),
            'creb': config.get('imageSource', {}).get('local_path_creb'),
            'oreb': config.get('imageSource', {}).get('local_path_oreb'),
        }
        local_paths = {key: path for key, path in local_paths.items() if path}

        if local_paths:
            return 'local', local_paths

        if 'url_host' in config['imageSource']:
            return 'url', config['imageSource']['url_host']

    except Exception as e:
        error_msg = f"Error configuring image source: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e


def init_image_output(config):
    """
    Initialize image output path.

    Args:
        config (ConfigParser): Configuration object.

    Returns:
        output_path: Image output path.
    """
    try:
        output_floorplans_path = config['imageOutput']['floorplans_path']
        output_surveys_path = config['imageOutput']['surveys_path']
        return output_floorplans_path, output_surveys_path
    except Exception as e:
        error_msg = f"Error configuring image output: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e


def init_AI_config(config):
    """
    Initialize model configuration parameters.

    Args:
        config (ConfigParser): Configuration object.

    Returns:
        dict: A dictionary containing parameters for each model.
    """
    try:
        AI_configs = {}

        # OpenAI configuration
        if 'openAI' in config:
            openai_config = {
                'key': config['openAI'].get('key', None),
                'endpoint': config['openAI'].get('endpoint', None),
                'orgID': config['openAI'].get('orgID', None),
                'projectID': config['openAI'].get('projectID', None),
                'model': config['openAI'].get('model', 'gpt-4o-mini'),
                'batchUrl': config['openAI'].get('batchUrl', None),
            }
            AI_configs['openai'] = openai_config

        # Gemini configuration
        if 'gemini' in config:
            gemini_config = {
                'key': config['gemini'].get('key', None),
                'model': config['gemini'].get('model', 'gemini-1.5-flash'),
            }
            AI_configs['gemini'] = gemini_config

        # Claude configuration
        if 'claude' in config:
            claude_config = {
                'key': config['claude'].get('key', None),
                'endpoint': config['claude'].get('endpoint', None),
                'model': config['claude'].get('model', 'claude-3-haiku-20240307'),
            }
            AI_configs['claude'] = claude_config

        return AI_configs
    except Exception as e:
        error_msg = f"Error initializing AI model configuration: {e}"
        logger.get_logger().error(error_msg)
        raise RuntimeError(error_msg) from e