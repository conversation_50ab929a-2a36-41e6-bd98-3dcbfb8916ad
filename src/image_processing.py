import os
import json
import re
import signal
import tempfile
import time
import hashlib
import shutil
import sys
from PIL import Image
from urllib.parse import urlparse
import requests
from requests.exceptions import RequestException
import nanoid
from floorplan_classification.main import make_pred
import logger
from src.utils import generate_nano_id
MAX_CONSECUTIVE_FAILURES_GET = 5
MAX_CONSECUTIVE_FAILURES_SAVE = 2
FAILURE_WINDOW_SECONDS_GET = 10  # Time window for failures in seconds
FAILURE_WINDOW_SECONDS_SAVE = 10
failure_timestamps_get = []  # List to store timestamps of failures
failure_timestamps_save = []

def run_classifier(model_manager, data_path) -> str:
    """
    Run the classifier on the images at the specified data path.

    Args:
        model_manager (ModelManager): An instance of ModelManager containing the loaded model and class names.
        data_path (str): The path to the data (images) on which the classifier will be run.

    Returns:
        str: The predicted class for the images.
    """
    logger.get_logger().debug("Running classifier on images in %s", data_path)
    predicted_class = make_pred(
        model_manager.class_names, model_manager.model, data_path)
    logger.get_logger().info(
        "Image %s predict result is %s", data_path, predicted_class)
    return predicted_class


def classifier_and_process_file(model_manager, saved_file, _id, pic_path_original, img_floorplans_output_path, img_surveys_output_path) -> dict:
    """
    Classify the image and process the file based on the predicted class.

    Args:
        model_manager (ModelManager): An instance of ModelManager containing the loaded model and class names.
        saved_file (str): The path to the saved image file.
        _id (str): The unique identifier associated with the image.
        pic_path_original (str): The original path of the image.
        img_floorplans_output_path (str): The path to the output directory for floorplans.
        img_surveys_output_path (str): The path to the output directory for surveys.

    Returns:
        dict: A dictionary containing the original filename, predicted class, and output path.
    """
    predicted_class = run_classifier(model_manager, saved_file)

    if predicted_class in ["floor_plans", "surveys"]:
        # Generate a Nano ID for the output filename
        nano_foldername = generate_nano_id(1)
        nano_filename = generate_nano_id(9)

        # Extract the original filename and file format from the saved file path
        original_filename = os.path.basename(saved_file)
        file_format = original_filename.split('.')[-1]

        img_output_path = img_floorplans_output_path
        if predicted_class == "surveys":
            img_output_path = img_surveys_output_path
        # Construct the output path with UUID
        output_dir = os.path.join(
            img_output_path, nano_foldername)
        saved_output_filepath = os.path.join(
            nano_foldername, f"{nano_filename}.{file_format}")
        output_filepath = os.path.join(
            output_dir, f"{nano_filename}.{file_format}")

        # Obtain image metadata (width and height)
        try:
            with Image.open(saved_file) as img:
                width, height = img.size
        except Exception as e:
            logger.get_logger().error("Failed to get image metadata: {%s}", e)
            width, height = None, None

        # Calculate SHA256 hash of the image content
        try:
            with open(saved_file, 'rb') as f:
                img_hash = hashlib.sha256(f.read()).hexdigest()
        except Exception as e:
            logger.get_logger().error("Failed to calculate image hash: {%s}", e)
            img_hash = None

        # Prepare the result object with additional metadata
        result_obj = {
            "input": pic_path_original,
            "class": predicted_class,
            "output": saved_output_filepath,
            "w": width,
            "h": height,
            "imgHash": img_hash,
        }

        try:
            os.makedirs(output_dir, exist_ok=True)
            shutil.copy(saved_file, output_filepath)
            logger.get_logger().debug(
                "Copy file %s to %s", saved_file, output_filepath)
            failure_timestamps_save.clear()
        except Exception as e:
            error_msg = f"Error coping file {saved_file} to {output_filepath}: {e}"
            logger.get_logger().error(error_msg)
            check_consecutive_failures(failure_timestamps_save, FAILURE_WINDOW_SECONDS_SAVE,
                                       MAX_CONSECUTIVE_FAILURES_SAVE, 'save')
        return result_obj
    
    logger.get_logger().debug(
        "File classified as %s, no processing needed", predicted_class)
    return None


def download_and_process_images(data: dict, model_manager, img_source_type,
                                img_source, worker_id, speed_meter, queue, img_floorplans_output_path, img_surveys_output_path) -> dict:
    """
    Download and save images from URLs or copy images from the local file system provided in the 'pics' list of the input dictionary.

    Args:
        data (dict): Input dictionary containing '_id', 'pics', 'uaddr', and 'unit' keys.
        model_manager (ModelManager): An instance of ModelManager containing the loaded model and class names.
        img_source_type (str): The image source type.
        img_source (str): The image source.
        worker_id (int): The worker ID.
        speed_meter (SpeedMeter): Instance of the SpeedMeter class.
        img_count (Value): Shared Value for counting images processed.
        queue (Queue): Queue to store the results of the image processing.
        img_floorplans_output_path (str): The path to the output directory for floorplans.
        img_surveys_output_path (str): The path to the output directory for surveys.
    Returns:
        dict: A dictionary containing the input data and results of the image classification.
    """
    _id = data['_id']
    pics = data['pics']
    worker_id_str = f'work{worker_id}'
    save_dir = os.path.join(tempfile.gettempdir(),
                            'pyfpimg', worker_id_str, str(_id))
    os.makedirs(save_dir, exist_ok=True)
    uaddr = data['uaddr']
    unt = data['unt'] if 'unt' in data else None
    phoMt = data['phoMt']

    output_json = {
        "_id": _id,
        "uaddr": uaddr,
        "unt": unt,
        "phoMt": phoMt,
        "result": [],
        "errors": []
    }

    processed_images = 0

    for pic_path in pics:
        processed_images += 1
        speed_meter.check({'pic': 1})
        saved_file = get_img(pic_path, img_source_type, img_source, save_dir, output_json)
        if saved_file:
            result_obj = classifier_and_process_file(
                model_manager, saved_file, _id, pic_path, img_floorplans_output_path, img_surveys_output_path)
            if result_obj:
                output_json["result"].append(result_obj)

    queue.put(processed_images)
    logger.get_logger().debug("processed_images %s", processed_images)
    os.kill(os.getppid(), signal.SIGUSR1)
    try:
        shutil.rmtree(save_dir)
        logger.get_logger().debug("Deleted temporary directory %s", save_dir)
    except Exception as e:
        error_msg = f"Error deleting temporary directory {save_dir}: {e}"
        logger.get_logger().error(error_msg)

    logger.get_logger().info("Output json %s", json.dumps(output_json))
    return output_json

def is_url_path(pic_path):
    """
    Check if the image path is a URL.
    """
    return pic_path.startswith("http://") or pic_path.startswith("https://") or pic_path.startswith("//")


def download_url_img(pic_path, pic_path_new, save_dir, output_json):
    """
    Download an image from a URL and save it to the specified directory.

    Args:
        pic_path (str): The original image path.
        pic_path_new (str): The new image path.
        save_dir (str): The directory where the image will be saved.
        output_json (dict): The output JSON dictionary.

    Returns:
        str: The path to the saved image file, or None if an error occurred.
    """
    try:
        logger.get_logger().debug(
            "Trying to download image from URL: %s", pic_path_new)
        filename = os.path.basename(urlparse(pic_path_new).path)

        # Attempt to download the image with a 60-second timeout
        response = requests.get(pic_path_new, timeout=60)
        logger.get_logger().debug("HTTP GET request sent, awaiting response")

        # Check if the response was successful
        response.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)
        logger.get_logger().debug(
              "Image download successful, status code: %d", response.status_code)

        saved_file = os.path.join(save_dir, filename)
        with open(saved_file, 'wb') as f:
            f.write(response.content)
        logger.get_logger().debug("Image downloaded and saved: %s", saved_file)
        failure_timestamps_get.clear()
        return saved_file
    except RequestException as e:
        error_msg = f"Error processing image from URL: {pic_path_new}, Error: {e}"
        logger.get_logger().error(error_msg)
        output_json["result"].append({
            'input': pic_path,
            'class': '',
            'error': 'URL invalid'
        })
        output_json["errors"].append(
            {'input': pic_path, 'errorMessage': 'URL invalid', })
        check_consecutive_failures(failure_timestamps_get, FAILURE_WINDOW_SECONDS_GET,
                                   MAX_CONSECUTIVE_FAILURES_GET, 'get')
        return None


def check_consecutive_failures(failure_timestamps, failure_window_seconds, max_consecutive_failures, failure_type):
    """
    Check for consecutive failures within a time window and exit the program if the maximum number of failures is reached.
    
    Args:
        failure_timestamps (list): List of timestamps of failures.
        failure_window_seconds (int): The time window for failures in seconds.
        max_consecutive_failures (int): The maximum number of consecutive failures allowed.
        failure_type (str): The type of failure (e.g., 'save' or 'get
        
    Returns:
        None
    """
    current_time = time.time()
    failure_timestamps.append(current_time)

    # Remove timestamps older than the failure window
    failure_timestamps = [
        ts for ts in failure_timestamps if current_time - ts <= failure_window_seconds]

    logger.get_logger().debug(
        f"Consecutive {failure_type} failures in the last {failure_window_seconds} seconds: {len(failure_timestamps)}")

    if len(failure_timestamps) >= max_consecutive_failures:
        error_msg = f"Max consecutive {failure_type} failures within the time window reached. Exiting the program."
        logger.get_logger().error(error_msg)
        raise OSError(error_msg)

def get_local_img(pic_path, img_source, output_json):
    """
    Get the local image path.

    Args:
        pic_path (str): The image path.
        img_source (dict): A dictionary of image sources with prefixes as keys.
        output_json (dict): The output JSON dictionary.

    Returns:
        str: The local image path, or None if the image is not found.
    """
    # Extract the prefix from the pic_path
    prefix_match = re.match(r'^/?([^/]+)', pic_path)
    prefix = prefix_match.group(1)

    if not prefix_match or prefix not in img_source:
        logger.get_logger().error("Invalid image path: %s", pic_path)
        output_json["result"].append({
            'input': pic_path,
            'class': '',
            'error': 'Invalid image path'
        })
        output_json["errors"].append({
            'input': pic_path,
            'errorMessage': 'URL invalid',
        })
        return None

    # Remove the first one or two parts of the path
    if prefix in ['trb', 'cda']:
        cleaned_path = re.sub(r'^(?:/)?([^/]+/){2}', '', pic_path)
    if prefix in ['orb', 'creb', 'oreb']:
        cleaned_path = re.sub(r'^(?:/)?([^/]+/){1}', '', pic_path)
    local_path = os.path.join(
        img_source[prefix].rstrip('/'), cleaned_path.lstrip('/'))

    if not os.path.exists(local_path):
        logger.get_logger().error("Local image not found: %s", local_path)
        output_json["result"].append({
            'input': pic_path,
            'class': '',
            'error': 'Local image not found'
        })
        output_json["errors"].append(
            {'input': pic_path, 'errorMessage': 'URL invalid', })
        check_consecutive_failures(failure_timestamps_get, FAILURE_WINDOW_SECONDS_GET,
                                   MAX_CONSECUTIVE_FAILURES_GET, 'get')
        return None
    failure_timestamps_get.clear()
    return local_path


def get_img(pic_path, img_source_type, img_source, save_dir, output_json):
    """
    Get the image path based on the image source type.
    
    Args:    
        pic_path (str): The image path.
        img_source_type (str): The image source type.
        img_source (str): The image source.
        save_dir (str): The directory where the image will be saved.
        output_json (dict): The output JSON dictionary.
    
    Returns:
        str: The image path, or None if an error occurred.
    """
    pic_path_new = pic_path
    if img_source_type == 'url' and not is_url_path(pic_path):
        if not pic_path.startswith("/"):
            pic_path_new = f"https://{img_source}/{pic_path}"
        else:
            pic_path_new = f"https://{img_source}{pic_path}"
    if is_url_path(pic_path_new):
        if pic_path.startswith("//"):
            pic_path_new = f"https:{pic_path}"
        return download_url_img(pic_path, pic_path_new, save_dir, output_json)
    return get_local_img(pic_path, img_source, output_json)