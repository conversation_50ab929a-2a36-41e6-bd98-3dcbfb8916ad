import logging

default_log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logger_factory = {}
_ID = None


def new_logger(identifier, log_file, log_level=logging.DEBUG, log_format=default_log_format, encoding='utf-8'):
    """
    Create a new logger instance.

    Args:
        identifier (str): Identifier for the logger instance.
        log_file (str): Path to the log file.
        log_level (int): Log level for the logger instance.
        log_format (str): Log format for the logger instance.
        encoding (str): Encoding for the logger instance.

    Returns:
        logging.Logger: The logger instance.
    """
    if identifier is None:
        raise ValueError(
            "Invalid logger parameter 'identifier': identifier must be defined")
    if log_file is None:
        raise ValueError(
            "Invalid logger parameter 'log_file': log_file must be defined")

    if identifier in logger_factory:
        return logger_factory[identifier]
    else:
        logging.basicConfig(level=logging.DEBUG)

        fh = logging.FileHandler(log_file, encoding=encoding)
        fh.setLevel(log_level)
        fh.setFormatter(logging.Formatter(log_format))

        logger_factory[identifier] = logging.getLogger(identifier)
        logger_factory[identifier].addHandler(fh)

        global _ID
        _ID = identifier

        return logger_factory[identifier]


def get_logger(logger_id=None):
    if logger_id is not None:
        if logger_id not in logger_factory:
            raise ValueError(
                f"Invalid logger_id: Identifier '{logger_id}' not found in logger_factory")
        return logger_factory[logger_id]

    global _ID
    if _ID is None:
        raise ValueError(
            "Invalid logger id 'None', 'get_logger' can not be called before calling 'new_logger'")

    if _ID not in logger_factory:
        raise ValueError(
            f"Invalid Default ID: '{_ID}' not found in logger_factory")

    return logger_factory[_ID]