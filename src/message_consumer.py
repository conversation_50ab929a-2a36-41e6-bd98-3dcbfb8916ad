import json
from confluent_kafka import Kafka<PERSON><PERSON>r, KafkaException
from image_processing import download_and_process_images
import logger


def consume_and_process_messages(kafka_consumer_init, kafka_consumer_routine,
                                 kafka_producer, mongo_client, model_manager,
                                 img_source_type, img_source, should_continue,
                                 worker_id, speed_meter, queue, img_floorplans_output_path, img_surveys_output_path) -> None:
    """
    Kafka consumer that consumes messages from a specified topic and processes them.

    Args:
        kafka_consumer_init (KafkaConsumer): Instance of the KafkaConsumer class.
        kafka_consumer_routine (KafkaConsumer): Instance of the KafkaConsumer class.
        kafka_producer (KafkaProducer): Instance of the KafkaProducer class.
        mongo_client (MongoDBClient): Instance of the MongoDBClient class.
        model_manager (ModelManager): Instance of the ModelManager class.
        img_source_type (str): The image source type.
        img_source (str): The image source.
        worker_id (int): The worker ID.
        speed_meter (SpeedMeter): Instance of the SpeedMeter class.
        should_continue (dict): Dictionary containing the 'running' key to indicate 
                                if the consumer should continue running.    
        queue (Queue): Queue to store the results of the image processing.
        img_floorplans_output_path (str): The path to the output directory for floorplans.
        img_surveys_output_path (str): The path to the output directory for surveys.
    Returns:
        None
    """

    try:
        while should_continue['running']:
            if not should_continue['running']:
                logger.get_logger().debug(
                    "in while should_continue['running'] = %s", should_continue['running'])
                break
            msg = get_kafka_msg(kafka_consumer_init,
                                kafka_consumer_routine, speed_meter)
            if msg is None:
                logger.get_logger().debug("No message received")
                continue
            if msg.error():
                if msg.error().code() == KafkaError.UNKNOWN_TOPIC_OR_PART:
                    logger.get_logger().error("Subscribed topic not available, will retry: %s", msg.error())
                    continue
                logger.get_logger().error("Consumer error: %s", msg.error())
                continue

            value = msg.value().decode('utf-8')
            try:
                value = json.loads(value)
                logger.get_logger().info("Received message: %s", value)

                if not (validate_kafka_data_format(value)):
                    logger.get_logger().debug("Invalid message format: %s", value)
                    continue

                result_json = download_and_process_images(
                    value, model_manager, img_source_type,
                    img_source, worker_id, speed_meter, queue, img_floorplans_output_path, img_surveys_output_path)
                logger.get_logger().debug("Result JSON: %s", result_json)

                kafka_producer.produce(result_json)
                logger.get_logger().debug(
                    "Message sent to Kafka topic %s", kafka_producer.topic)
                mongo_client.write(result_json)
                logger.get_logger().debug("Result written to MongoDB")
            except OSError as e:
                logger.get_logger().error(f"File processing error: {str(e)}")
                return
            except json.JSONDecodeError as e:
                error_msg = f"Failed to decode message: {msg.value()}, error: {e}"
                logger.get_logger().error(error_msg)
                raise ValueError(error_msg) from e
            except Exception as e:
                error_msg = f"Error sending message to Kafka/write MongoDB: {e}"
                logger.get_logger().error(error_msg)
                raise RuntimeError(error_msg) from e
    except KafkaException as e:
        logger.get_logger().error("Kafka error: %s", e)
        raise RuntimeError(
            "Kafka error occurred. Stopping the main process.") from e
    finally:
        kafka_consumer_routine.close()
        kafka_consumer_init.close()
        logger.get_logger().debug("Kafka consumer closed.")


def get_kafka_msg(kafka_consumer_init, kafka_consumer_routine, speed_meter):
    """
    Get the next message from the Kafka consumer.

    Args:
        kafka_consumer_init (KafkaConsumer): Instance of the KafkaConsumer class.
        kafka_consumer_routine (KafkaConsumer): Instance of the KafkaConsumer class.
        speed_meter (SpeedMeter): Instance of the SpeedMeter class.

    Returns:
        Message: The next message from the Kafka consumer.
    """
    # Check routine topic first, timeout read from config and set in KafkaConsumer
    msg_routine = kafka_consumer_routine.poll()
    if msg_routine is None:
        msg = kafka_consumer_init.poll()  # Only poll init topic if no routine message
        logger.get_logger().debug(
            "No routine message, trying to get init message")
        if msg is None:
            speed_meter.check({'init_msg': 1})
            logger.get_logger().debug("No init message received")
    else:
        msg = msg_routine
        speed_meter.check({'routine_msg': 1})
        logger.get_logger().debug("Routine message received")
    return msg


def validate_kafka_data_format(data):
    """
    Validate the format of the received data.

    Args:
        data (dict): Received data.

    Returns:
        bool: True if data format is valid, False otherwise.
    """
    # Check if data contains required fields
    required_fields = ["_id", "uaddr", "pics"]
    for field in required_fields:
        if field not in data:
            logger.get_logger().debug(
                "Missing required field '%s' in data: %s", field, data)
            return False

    # Check if uaddr is not empty
    if not data.get("uaddr"):
        raise ValueError(f"Field 'uaddr' must not be empty: {data}")

    # Check if unt is not empty
    # if not data.get("unt"):
    #     logger.get_logger().info(f"Field 'unt' must not be empty: {data}")

    # Check if _id is a string
    if not isinstance(data["_id"], str):
        raise ValueError(f"Field '_id' must be a string: {data}")

    # Check if pics is a list
    if not isinstance(data["pics"], list):
        raise ValueError(f"Field 'pics' must be a list: {data}")

    return True
