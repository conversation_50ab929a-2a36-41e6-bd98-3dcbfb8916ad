import json
import time
import os
from datetime import datetime
import logger
from src.floorplan_classification.image_wrangling import remove_file
from src.helpers.openAI_batch import (
    check_batch_status, create_batch, create_client, 
    retrieve_batch_results, upload_batch_input_file
)


def process_jsonl_line_by_line(master_jsonl_path: str, api_key: str, mongo_client, pdf_list: list):
    """
    process line by line for total jsonl file, create temp file for each line and process separately
    
    Args:
        master_jsonl_path (str): total jsonl file path
        api_key (str): OpenAI API key
        mongo_client: MongoDB client
        pdf_list (list): pdf list, for image count validation
    """
    if not os.path.exists(master_jsonl_path):
        raise FileNotFoundError(f"Master JSONL file not found: {master_jsonl_path}")
    
    # create pdf id to image count mapping
    pdf_image_counts = {}
    for pdf_dict in pdf_list:
        pdf_id = pdf_dict.get('_id')
        image_count = len(pdf_dict.get('encoded_images', []))
        if pdf_id:
            pdf_image_counts[pdf_id] = image_count
    
    # read total jsonl file, count total lines
    total_lines = 0
    with open(master_jsonl_path, 'r') as f:
        for line in f:
            if line.strip():
                total_lines += 1
    
    logger.get_logger().info(f"Starting line-by-line processing of {total_lines} entries from {master_jsonl_path}")
    
    processed_count = 0
    failed_count = 0
    
    # process line by line
    with open(master_jsonl_path, 'r') as f:
        for line_num, line in enumerate(f, 1):
            if not line.strip():
                continue
            
            try:
                # parse current line
                entry = json.loads(line.strip())
                custom_id = entry.get('custom_id', f'line_{line_num}')
                
                logger.get_logger().info(f"Processing entry {line_num}/{total_lines}: {custom_id}")
                
                # create temp file
                temp_file = create_temp_jsonl_file(entry, custom_id)
                
                try:
                    # process single entry
                    success = process_single_entry(
                        temp_file, api_key, mongo_client, 
                        custom_id, pdf_image_counts
                    )
                    
                    if success:
                        processed_count += 1
                        logger.get_logger().info(f"✅ Successfully processed {custom_id} ({processed_count}/{total_lines})")
                    else:
                        failed_count += 1
                        logger.get_logger().error(f"❌ Failed to process {custom_id}")
                
                finally:
                    # clean up temp file
                    remove_file(temp_file)
                
                # add short delay between processing to avoid api limit
                if line_num < total_lines:
                    logger.get_logger().debug("Waiting 3 seconds before next entry...")
                    time.sleep(3)
                
            except json.JSONDecodeError as e:
                logger.get_logger().error(f"JSON decode error at line {line_num}: {e}")
                failed_count += 1
                continue
            except Exception as e:
                logger.get_logger().error(f"Error processing line {line_num}: {e}")
                failed_count += 1
                continue
    
    # process completed summary
    logger.get_logger().info(f"Line-by-line processing completed:")
    logger.get_logger().info(f"  Total entries: {total_lines}")
    logger.get_logger().info(f"  Successfully processed: {processed_count}")
    logger.get_logger().info(f"  Failed: {failed_count}")
    logger.get_logger().info(f"  Success rate: {processed_count/total_lines*100:.1f}%")


def create_temp_jsonl_file(entry: dict, custom_id: str) -> str:
    """
    create temp jsonl file for single entry
    
    Args:
        entry (dict): jsonl entry
        custom_id (str): custom id
        
    Returns:
        str: temp file path
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # include milliseconds
    temp_file = f'logs/temp_single_{custom_id}_{timestamp}.jsonl'
    
    with open(temp_file, 'w') as f:
        json.dump(entry, f)
        f.write('\n')
    
    logger.get_logger().debug(f"Created temporary file: {temp_file}")
    return temp_file


def process_single_entry(temp_file: str, api_key: str, mongo_client, custom_id: str, pdf_image_counts: dict) -> bool:
    """
    process single jsonl entry
    
    Args:
        temp_file (str): temp jsonl file path
        api_key (str): OpenAI API key
        mongo_client: MongoDB client
        custom_id (str): custom id
        pdf_image_counts (dict): pdf image counts
        
    Returns:
        bool: process success or not
    """
    try:
        create_client(api_key)
        
        # upload temp file
        logger.get_logger().debug(f"Uploading temporary file: {temp_file}")
        batch_input_file_id = upload_batch_input_file(temp_file)
        if not batch_input_file_id:
            logger.get_logger().error(f"Failed to upload file for {custom_id}")
            return False
        
        # create batch task
        batch_id = create_batch(
            batch_input_file_id,
            description=f"single entry for {custom_id}"
        )
        if not batch_id:
            logger.get_logger().error(f"Failed to create batch for {custom_id}")
            return False
        
        logger.get_logger().info(f"Batch {batch_id} created for {custom_id}")
        
        # wait for batch completion
        max_wait_time = 20 * 60  # 20 minutes max wait time
        wait_interval = 15  # 15 seconds check once
        total_waited = 0
        
        while total_waited < max_wait_time:
            batch_status = check_batch_status(batch_id)
            logger.get_logger().debug(f"Batch {batch_id} status: {batch_status.status}")
            
            if batch_status.status == 'completed':
                if not batch_status.output_file_id:
                    logger.get_logger().error(f"Batch {batch_id} completed but no output file")
                    return False
                
                # process result
                results = retrieve_batch_results(batch_status.output_file_id)
                success = process_single_result(results, mongo_client, custom_id, pdf_image_counts)
                
                if success:
                    logger.get_logger().info(f"Successfully processed result for {custom_id}")
                    return True
                else:
                    logger.get_logger().error(f"Failed to process result for {custom_id}")
                    return False
                    
            elif batch_status.status == 'failed':
                error_msg = f"Batch {batch_id} failed for {custom_id}"
                if hasattr(batch_status, 'errors') and batch_status.errors:
                    error_msg += f": {batch_status.errors}"
                logger.get_logger().error(error_msg)
                return False
                
            elif batch_status.status in ['expired', 'cancelled']:
                logger.get_logger().error(f"Batch {batch_id} was {batch_status.status} for {custom_id}")
                return False
            
            # wait for next check
            time.sleep(wait_interval)
            total_waited += wait_interval
        
        # timeout
        logger.get_logger().error(f"Batch {batch_id} timed out for {custom_id}")
        return False
        
    except Exception as e:
        logger.get_logger().error(f"Error processing single entry {custom_id}: {e}")
        return False


def process_single_result(results: str, mongo_client, custom_id: str, pdf_image_counts: dict) -> bool:
    """
    process single batch result, save to mongo
    
    Args:
        results (str): batch result string
        mongo_client: MongoDB client
        custom_id (str): custom id
        pdf_image_counts (dict): pdf image counts
        
    Returns:
        bool: process success or not
    """
    try:
        result_list = results.split('\n')
        for result in result_list:
            if result.strip():
                result_json = json.loads(result)
                result_custom_id = result_json['custom_id']
                
                if result_custom_id != custom_id:
                    logger.get_logger().warning(f"Custom ID mismatch: expected {custom_id}, got {result_custom_id}")
                
                # extract ai result
                ai_result_str = result_json['response']['body']['choices'][0]['message']['content']
                ai_result_str = ai_result_str.replace('\n', '').replace(
                    '\r', '').replace('```json', '').replace('```', '').strip()
                
                logger.get_logger().debug(f"AI Result for {result_custom_id}: {ai_result_str}")
                ai_result = json.loads(ai_result_str)
                
                # ensure result is array and validate count
                if isinstance(ai_result, dict):
                    ai_result = [ai_result]
                    logger.get_logger().warning(f"AI returned single object for {result_custom_id}, wrapped in array")
                elif isinstance(ai_result, list):
                    # validate count match
                    returned_count = len(ai_result)
                    expected_count = pdf_image_counts.get(result_custom_id)
                    
                    if expected_count is not None:
                        if returned_count == expected_count:
                            logger.get_logger().debug(f"AI returned array with {returned_count} items for {result_custom_id} (matches input)")
                        else:
                            logger.get_logger().warning(f"AI returned array with {returned_count} items for {result_custom_id}, but expected {expected_count} (input images count mismatch)")
                    else:
                        logger.get_logger().debug(f"AI returned array with {returned_count} items for {result_custom_id}")
                else:
                    logger.get_logger().error(f"Unexpected AI result type for {result_custom_id}: {type(ai_result)}")
                    return False
                
                # save to mongo
                mongo_client.update_ai_result(result_custom_id, ai_result)
                return True
        
        return False
        
    except Exception as e:
        logger.get_logger().error(f"Error processing result for {custom_id}: {e}")
        return False
