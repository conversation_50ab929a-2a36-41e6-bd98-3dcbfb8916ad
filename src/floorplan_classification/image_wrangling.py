import shutil
import random
from pathlib import Path
import os
import fitz
import base64
from src.utils import generate_nano_id


def remove_file(filepath):
    """Remove file if it exists."""
    try:
        os.remove(filepath)
    except FileNotFoundError:
        pass


def extract_high_res_images_from_pdf_dir(pdf_dir, output_folder, dpi=600):
    """
    Iterate over all PDF files in a directory and extract high-resolution images from each.

    :param pdf_dir: Path to the directory containing PDF files.
    :param output_folder: Folder where the images will be saved.
    :param dpi: Resolution of the output images (dots per inch).

    :return: List of dictionaries containing the extracted images.
    [ {pdf_name: str, pdf_path: str, images: ['img1','img2'], encoded_images: ['img1_encode','img2_encode']} ]
    """
    # Ensure the output folder exists
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    pdf_list = []

    # Iterate over each PDF file in the directory
    for pdf_file in Path(pdf_dir).rglob('*.pdf'):
        pdf_dict = extract_images_from_pdf(pdf_file, output_folder, dpi=dpi)
        pdf_list.append(pdf_dict)
    return pdf_list


def generate_nano_path_and_id(output_folder):
    """
    Generate a random folder name and file name for saving an image.
    
    :param output_folder: Folder where the images will be saved.
    
    :return: Tuple containing the output file path and the saved output file path.
    """
    nano_foldername = generate_nano_id(1)
    nano_filename = generate_nano_id(9)

    output_dir = os.path.join(
            output_folder, nano_foldername)
    saved_output_filepath = os.path.join(
            nano_foldername, f"{nano_filename}.png")
    output_filepath = os.path.join(
            output_dir, f"{nano_filename}.png")
    os.makedirs(output_dir, exist_ok=True)
    return output_filepath, saved_output_filepath


def extract_images_from_pdf(pdf_file, output_folder, prefix="pdf_page", dpi=600, pdf_dict=None):
    """
    Extract high-resolution images from a single PDF file and save them as separate files in the output folder.

    :param pdf_file: Path to the PDF file.
    :param output_folder: Folder where the images will be saved.
    :param prefix: Prefix for the saved image files.
    :param dpi: Resolution of the output images (dots per inch).

    :return: Dictionary containing the extracted images.
    """
    pdf_filename = Path(pdf_file).stem  # Get the PDF file name without extension
    prefix = pdf_filename  # Set the prefix to the PDF file name
    pdf_doc = fitz.open(pdf_file)
    # pdf_dict = {'pdf_name': pdf_filename, 'pdf_path': pdf_file,
    #             'images': [], 'encoded_images': []}
    if pdf_dict is None:
        pdf_dict = {'images': [], 'encoded_images': []}

    # Iterate over each page in the PDF
    for page_index, page in enumerate(pdf_doc):
        # output img: uuid1/uuid9.jpeg 
        output_filepath, saved_output_filepath = generate_nano_path_and_id(output_folder)
        # Create the file path for the output image
        # filepath = os.path.join(output_folder, f'{prefix}_{page_index + 1}.png')

        # Get the page and convert it to an image
        zoom_x = dpi / 72.0  # Horizontal zoom factor
        zoom_y = dpi / 72.0  # Vertical zoom factor
        matrix = fitz.Matrix(zoom_x, zoom_y)
        # Use the matrix for high resolution
        pix = page.get_pixmap(matrix=matrix, alpha=False)

        # Remove the existing file if it exists
        remove_file(output_filepath)

        # Save the image
        pix.save(output_filepath)

        print(f"Saved {output_filepath}")
        pdf_dict['images'].append(saved_output_filepath)
        pdf_dict['encoded_images'].append(encode_image(output_filepath))
    return pdf_dict

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def delete_files_in_directory(directory_path):
    try:
        # List all files in the specified directory
        files = os.listdir(directory_path)
        # Iterate through each file and delete them
        for file_name in files:
            file_path = os.path.join(directory_path, file_name)
            if os.path.isfile(file_path):
                os.remove(file_path)
                # print(f"Deleted: {file_path}")

        print(f"All files at {directory_path} deleted successfully.")

    except Exception as e:
        print(f"An error occurred: {e}")


def copy_images(data_dir, out_dir, prefix, prob=0.0):
    """
    Copy images from data_dir to out_dir with a given probability.

    Args:
        data_dir (str): Directory containing the original images.
        out_dir (str): Directory where images will be copied.
        prefix (str): Prefix for the new filenames.
        prob (float): Probability to select images for copying. From 0 to 1.
    """
    num = 0
    print(f"Copying images from {data_dir} to {out_dir}")

    if not os.path.exists(out_dir):
        os.makedirs(out_dir)

    for file in os.listdir(data_dir):
        if random.random() > prob:
            new_filename = f'{prefix}_{num}.jpg'
            new_path = os.path.join(out_dir, new_filename)
            original_path = os.path.join(data_dir, file)
            # Check if the file already exists
            if not os.path.exists(new_path):
                shutil.copy2(original_path, new_path)
                num += 1
            else:
                print(f"File {new_path} already exists, skipping.")

    print(f"Done copying images from {data_dir}")
