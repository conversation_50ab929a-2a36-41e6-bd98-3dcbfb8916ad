import shutil
import random
from pathlib import Path
import os
import fitz


def removeFile(filepath):
    """Remove file if it exists."""
    try:
        os.remove(filepath)
    except FileNotFoundError:
        pass


def extract_high_res_images_from_pdf_dir(pdf_dir, output_folder, prefix="floor_plans", dpi=300):
    """
    Extract high-resolution images from all PDFs in a directory and save them as separate files in the output folder.

    :param pdf_dir: Path to the directory containing PDF files.
    :param output_folder: Folder where the images will be saved.
    :param prefix: Prefix for the saved image files.
    :param dpi: Resolution of the output images (dots per inch).
    """
    # Ensure the output folder exists
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # Iterate over each PDF file in the directory
    for pdf_file in Path(pdf_dir).rglob('*.pdf'):
        pdf_filename = pdf_file.stem  # Get the PDF file name without extension
        prefix = pdf_filename  # Set the prefix to the PDF file name
        pdf_doc = fitz.open(pdf_file)

        # Iterate over each page in the PDF
        for page_index, page in enumerate(pdf_doc):
            # Create the file path for the output image
            filepath = os.path.join(
                output_folder, f'{prefix}_{page_index + 1}.png')

            # Get the page and convert it to an image
            zoom_x = dpi / 72.0  # Horizontal zoom factor
            zoom_y = dpi / 72.0  # Vertical zoom factor
            matrix = fitz.Matrix(zoom_x, zoom_y)
            # Use the matrix for high resolution
            pix = page.get_pixmap(matrix=matrix, alpha=False)

            # Remove the existing file if it exists
            removeFile(filepath)

            # Save the image
            pix.save(filepath)

            print(f"Saved {filepath}")


def delete_files_in_directory(directory_path):
    try:
        # List all files in the specified directory
        files = os.listdir(directory_path)
        # Iterate through each file and delete them
        for file_name in files:
            file_path = os.path.join(directory_path, file_name)
            if os.path.isfile(file_path):
                os.remove(file_path)
                # print(f"Deleted: {file_path}")

        print(f"All files at {directory_path} deleted successfully.")

    except Exception as e:
        print(f"An error occurred: {e}")


def copy_images(data_dir, out_dir, prefix, prob=0.0):
    """
    Copy images from data_dir to out_dir with a given probability.

    Args:
        data_dir (str): Directory containing the original images.
        out_dir (str): Directory where images will be copied.
        prefix (str): Prefix for the new filenames.
        prob (float): Probability to select images for copying. From 0 to 1.
    """
    num = 0
    print(f"Copying images from {data_dir} to {out_dir}")

    if not os.path.exists(out_dir):
        os.makedirs(out_dir)

    for file in os.listdir(data_dir):
        if random.random() > prob:
            new_filename = f'{prefix}_{num}.jpg'
            new_path = os.path.join(out_dir, new_filename)
            original_path = os.path.join(data_dir, file)
            # Check if the file already exists
            if not os.path.exists(new_path):
                shutil.copy2(original_path, new_path)
                num += 1
            else:
                print(f"File {new_path} already exists, skipping.")

    print(f"Done copying images from {data_dir}")
