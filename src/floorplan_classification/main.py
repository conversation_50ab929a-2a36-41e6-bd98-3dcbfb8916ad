"""
Add folder creation
"""

import os
import sys
import shutil
import argparse
import numpy as np
import datetime
import tensorflow as tf
from floorplan_classification.image_wrangling import extract_high_res_images_from_pdf_dir
from floorplan_classification.delete_all import clear_dirs
from floorplan_classification.data_preparation import sharp_and_res
from floorplan_classification.data_preparation import distribute_images
from floorplan_classification.data_preparation import augmentation
from floorplan_classification.image_wrangling import copy_images
from floorplan_classification.prediction import preprocess_new_data
from floorplan_classification.model import CNN
from floorplan_classification.model import load_data_to_tf_dataset
from floorplan_classification.model import ModelManager

os.environ["CUDA_VISIBLE_DEVICES"] = "-1"
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


FACTOR = 5  # factor for image enhance - more it is, more sharppened the image is going to be
LIMIT = 7  # limit for CLAHE prepocessing for survey images - more it is the more drastic changes are going to be applied
HEIGHT = 224
WIDTH = 224


class CustomException(Exception):
    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


class DataHolder:

    """
    Holder class to keep the names of the va path variables during the program run
    """

    def __init__(self):
        self.floor_plan_train = None
        self.survey_train = None
        self.other_train = None
        self.predict = None


def preprocess(factor, clipLimit):
    """
    apply preprocessing -> divide to train/test/val -> augmentation on surveys -> save
    """

    sharp_and_res(factor=factor, clipLimit=clipLimit)  # apply sharpening
    print("Transformed Images")

    distribute_images()  # divide to train/test/val   # change function name
    print("Made dataset")

    # apply augmentation on each set, for each survey image
    augmentation('dataset/train/surveys',
                 'dataset/train/floor_plans', 'surveys')
    augmentation('dataset/validation/surveys',
                 'dataset/validation/floor_plans', 'surveys')

    print('Done augmentation')


def train_model_and_save(train_dataset, validation_dataset, test_dataset, class_names, num_classes):
    """
    train and eval CNN net
    """

    CNN_net = CNN(num_classes)
    history = CNN_net.train(train_dataset, validation_dataset, epochs=10)
    CNN_net.plot_training_hist(
        history, '3-layers CNN', ['red', 'orange'], ['blue', 'green'])
    f1_score = CNN_net.evaluate_model(test_dataset, class_names)

    current_date = datetime.datetime.now().strftime("%Y%m%d")
    MODEL_PATH_SAVE_NAME = f"../model/FPC_{current_date}_{int(f1_score*1000):04d}_k2140"

    # 保存为HDF5格式（.h5）
    h5_model_path = MODEL_PATH_SAVE_NAME + ".h5"
    CNN_net.save(h5_model_path)

    # 保存为Keras格式（.keras）
    keras_model_path = MODEL_PATH_SAVE_NAME + ".keras"
    CNN_net.save(keras_model_path)
    return keras_model_path


def make_pred(class_names, loaded_model, file_path):
    """
    make predictons on one new data

    :param class_names: load from the txt file (modified during train)
    :param loaded_model: model
    :param file_path: tets file path
    :return:
    """

    img = preprocess_new_data(file_path, FACTOR, LIMIT, 224, 224)
    img = tf.expand_dims(img, axis=0)  # Add batch dimension

    # Make prediction using the loaded model
    prediction = loaded_model.predict(img)

    predicted_class_index = np.argmax(prediction, axis=1)[0]
    predicted_class = class_names[predicted_class_index]

    return predicted_class


def make_preds(class_names, loaded_model, data_path):
    try:
        shutil.copytree(data_path, 'pred', dirs_exist_ok=True)
        file_paths = [os.path.join('pred', f)
                    for f in os.listdir('pred')]

        # Make predictions
        predictions = []
        for file_path in file_paths:
            predicted_class = make_pred(class_names, loaded_model, file_path)
            predictions.append([file_path, predicted_class])
        return predictions
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        sys.exit(1)


def print_and_save_preds(preds_list, result_file):
    """
    print the predictions and save them to the file
    """
    for file, pred in preds_list:
        print(f"for {file} prediction is {pred}\n")

    with open(result_file, "w") as output_file:
        for file, pred in preds_list:
            output_file.write(f"{file}:{pred}\n")


def check_pdf_files(directory):
    files = os.listdir(directory)

    for file in files:
        if file.lower().endswith('.pdf'):
            return True

    return False


def training_model(holder):
    # 1. link images
    copy_images(data_dir=holder.other_train,
                out_dir='data/other_images', prefix='other')
    copy_images(data_dir=holder.survey_train,
                out_dir='data/surveys', prefix='surveys')
    if check_pdf_files(holder.floor_plan_train):
        extract_high_res_images_from_pdf_dir(
            holder.floor_plan_train, 'data/floor_plans')
    else:
        copy_images(data_dir=holder.floor_plan_train, out_dir='data/floor_plans',
                    prefix='floor_plans')
    print("done copy")

    # 2. sharp+distribute+augmentation
    preprocess(factor=FACTOR, clipLimit=LIMIT)

    # 3. load data to tf.data.Dataset
    train_dataset, validation_dataset, test_dataset, class_names, num_classes = load_data_to_tf_dataset(
        "dataset/train",
        "dataset/validation",
        "dataset/test",
        img_height=HEIGHT, img_width=WIDTH)

    # 4. train and save
    keras_model_path = train_model_and_save(train_dataset, validation_dataset,
                                            test_dataset, class_names, num_classes)

    return keras_model_path


def main(train_save: int, predict: int, training_image_data: str, preds_image_data: str, model: str, result_file: str):
    """
    Define the way we will run load_dataset_train_preds_chain
    """
    if train_save and (not training_image_data):
        raise CustomException("Unspecified env var for training\n")
    if predict and (not preds_image_data):
        raise CustomException("Unspecified env var for predictions\n")
    if (not train_save) and predict and (not model):
        raise CustomException("Unspecified model file for predictions\n")

    holder = DataHolder()  # to hold paths for data during the run
    if train_save:
        root = training_image_data

        for image_folder_type in os.listdir(root):
            if image_folder_type == 'floorplan':
                holder.floor_plan_train = os.path.join(root, image_folder_type)
            elif image_folder_type == 'survey':
                holder.survey_train = os.path.join(root, image_folder_type)
            else:  # other images
                holder.other_train = os.path.join(root, image_folder_type)

        trained_model = training_model(holder)


    if predict:
        model_path = model if model else trained_model
        class_name_file = os.path.join(
            'src', 'floorplan_classification', 'class_names.txt')
        model_manager = ModelManager(model_path, class_name_file)
        preds_list = make_preds(model_manager.class_names,
                                model_manager.model, preds_image_data)
        print_and_save_preds(preds_list, result_file)
    clear_dirs()

if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("--train_save", type=int, choices=[0, 1],
                        help='load and prep data, then save the trained model', default=1)
    parser.add_argument("--predict", type=int,
                        choices=[0, 1], help='predict for new images', default=1)
    parser.add_argument("--image_training_data", help='name of the training directory',
                        default=os.getenv('image_training_data'))  # def - env var
    parser.add_argument("--image_prediction_data", help='name of the testing directory',
                        default=os.getenv('image_prediction_data'))
    parser.add_argument("--model_file", help='file of the model',
                        default=os.getenv('model_file'))
    parser.add_argument("--result_file", help='file of the predict result',
                        default=os.getenv('result_file'))

    args = parser.parse_args()
    MODEL_PATH_PREDS = args.model_file if args.model_file else None
    result_file = args.result_file if args.result_file else "./prediction_result.txt"

    main(args.train_save, args.predict, args.image_training_data,
         args.image_prediction_data, MODEL_PATH_PREDS, result_file)
