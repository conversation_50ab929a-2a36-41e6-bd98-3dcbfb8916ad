import numpy as np
from PIL import Image
from floorplan_classification.data_preparation import enhance
# export PYTHONPATH = "${PYTHONPATH}:/Users/<USER>/Documents/RealMaster/pyfpimg"

def preprocess_new_data(file_path, factor, clipLimit, img_height=224, img_width=224):
    """
    Apply the processing technique on the new image

    :param file_path:
    :param factor:
    :param img_height:
    :param img_width:
    :return:
    """
    img = Image.open(file_path)
    img = enhance(img, factor, (img_height, img_width))
    # img.save(file_path)

    # if re.findall(r'[Ss]urvey[sS]_\d+', file_path):
    #     img = apply_clahe(file_path,clipLimit,save=False) # not None, but image array

    img_array = np.array(img)
    return img_array
