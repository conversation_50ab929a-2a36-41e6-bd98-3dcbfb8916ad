# NOTE: do not run this file directly, use the run.sh script instead.
import threading
import time
import os
import argparse
from datetime import datetime
import signal
import sys
import toml
from multiprocessing import Pool, Manager, Queue
from message_consumer import consume_and_process_messages
from components_init import init_image_output, init_mongo, init_kafka_producer, init_kafka_consumer, init_model_manager, init_image_source, init_image_failure, init_url_host
from utils import graceful_exit, signal_handler_SIGUSR2
import logger
from helpers.SpeedMeter import SpeedMeter

os.environ["CUDA_VISIBLE_DEVICES"] = "-1"

start_ts = datetime.now()
total_img_count = 0
total_message_count = 0
timer = None

# speed_meter callback
def interval_callback(speed_meter):
    new_ts = datetime.now()
    process_min = (new_ts - start_ts).total_seconds() / 60
    logger.get_logger().info("Pyfpimg process time %s mins. %s",
                             process_min, speed_meter.to_string())


# sub process for each worker
def init_worker(config, should_continue, worker_id=None, speed_meter=None, queue=None, test_mode=False, failure_counter=None):
    try:
        log_file_name = f"logs/pyfpimg-{worker_id}.log"
        identifier = f'[worker:{worker_id}]'
        log_level = config['log']['level']
        sub_logger = logger.new_logger(identifier, log_file_name, log_level=log_level)
        sub_logger.info(
            'Worker %s initializing', worker_id)


        # Initialize components
        mongo_client = init_mongo(config)
        kafka_producer = init_kafka_producer(config, test_mode)
        kafka_consumer_init, kafka_consumer_routine = init_kafka_consumer(
            config, test_mode)
        model_manager = init_model_manager(config)
        img_source_type, img_source = init_image_source(config)
        img_url_host = init_url_host(config)
        img_floorplans_output_path, img_surveys_output_path = init_image_output(
            config)
        max_consecutive_failures_get, max_consecutive_failures_save, failure_window_seconds_get, failure_window_seconds_save = init_image_failure(config)

        def local_graceful_exit(sig, frame):
            graceful_exit(sig, frame, kafka_producer, mongo_client,
                          model_manager, kafka_consumer_init, kafka_consumer_routine, should_continue)

        signal.signal(signal.SIGINT, local_graceful_exit)
        signal.signal(signal.SIGTERM, local_graceful_exit)
        # Adding a delay to ensure all consumers are ready
        time.sleep(5)

        consume_and_process_messages(
            kafka_consumer_init, kafka_consumer_routine, kafka_producer, \
                mongo_client, model_manager, img_source_type, img_source, \
            should_continue, worker_id, speed_meter, queue, img_floorplans_output_path, \
            img_surveys_output_path, max_consecutive_failures_get, max_consecutive_failures_save,\
            failure_window_seconds_get, failure_window_seconds_save, img_url_host)

    except Exception as e:
        if isinstance(e, RuntimeError) or isinstance(e, OSError):
            # Stop the worker if a RuntimeError is encountered
            # failure_counter['count'] += 1
            failure_counter.value += 1
            sub_logger.error(
                'Worker %s encountered a critical error: %s', worker_id, str(e))
            raise
        
        # Log the error and continue for non-critical errors
        sub_logger.error('Worker %s encountered an error: %s',
                        worker_id, str(e))


def main(debug: bool = False):
    env_file_path = os.environ['RMBASE_FILE_PYTHON']

    parser = argparse.ArgumentParser(
        prog='PYML Organizer',
        description='Listen on kafka topics to sort out if a url or local file is a floorplan, survey or other, then save it to specified folder(s).',
        epilog='See /doc for more details or contact the author.')
    parser.add_argument('-f', '--config',
                        default=env_file_path,
                        help='config file path')
    parser.add_argument('--test',
                        action='store_true',
                        help='run in test mode')
    args = parser.parse_args()
    test_mode = args.test

    # Load the TOML config file
    if args.config:
        config_path = args.config
    else:
        config_path = os.environ['RMBASE_FILE_PYTHON']
    config_all = toml.load(config_path)
    config = config_all['pyfpimg']

    main_logger = logger.new_logger('main', config['log']['file'])
    main_logger.info('config => %s', str(config))


    speed_meter = SpeedMeter(
        interval_trigger_count=10,
        interval_callback=interval_callback)

    with Manager() as manager: # manager is used to share data between processes
        should_continue = manager.dict({'running': True})
        queue = manager.Queue()
        # failure_counter = manager.dict({'count': 0})
        failure_counter = manager.Value('i', 0)

        # Setup for kafka.request.init
        pool_size = int(config['kafka']['request'].get('worker_num', 5))
        init_args = [(config, should_continue, i, speed_meter, queue, test_mode, failure_counter)
                     for i in range(pool_size)]
        pool = Pool(pool_size)

        # print log every 2 minutes
        def print_main_speed_log():
            global total_img_count
            global timer
            process_min = (datetime.now() - start_ts).total_seconds() / 60
            main_logger.info(
                f"---All images processed: {total_message_count}, total images processed: {total_img_count} in {process_min:.2f} minutes")
            timer = threading.Timer(120, print_main_speed_log)
            timer.daemon = True
            timer.start()

        # handle signal.SIGINT and signal.SIGTERM
        def signal_handler_default(sig, frame):
            if timer is not None:
                timer.cancel()
            main_logger.info('Received signal: %s', sig)
            should_continue['running'] = False
            pool.terminate()
            pool.join()
            sys.exit(0)

        # print log when getting SIGUSR1 from sub process
        def signal_handler(sig, frame):
            global total_img_count
            global total_message_count
            while queue.qsize() > 0:
                get_img_count = queue.get()
                main_logger.info(f"Parent process received: {get_img_count}")
                total_img_count += get_img_count
                total_message_count += 1
            process_min = (datetime.now() - start_ts).total_seconds() / 60
            main_logger.info(
                f"---Total messages:{total_message_count}, total images processed: {total_img_count} in {process_min:.2f} minutes")

        def check_failure_count():
            global timer
            if failure_counter.value >= pool_size:
                main_logger.error(
                    'All workers failed. Terminating the program.')
                should_continue['running'] = False
                pool.terminate()
                pool.join()
                sys.exit(1)
            timer = threading.Timer(10, check_failure_count)  # check every 10s
            timer.daemon = True
            timer.start()
        check_failure_count()  # 启动定期检查

        signal.signal(signal.SIGINT, signal_handler_default)
        signal.signal(signal.SIGTERM, signal_handler_default)
        signal.signal(signal.SIGUSR2, signal_handler_SIGUSR2)
        signal.signal(signal.SIGUSR1, signal_handler)
        print_main_speed_log()

        try:
            for i in range(pool_size):
                pool.apply_async(init_worker, args=init_args[i])
            pool.close()
            print('Waiting for all subprocesses done...')
            pool.join()
        except KeyboardInterrupt:
            main_logger.info('KeyboardInterrupt received')
            signal_handler_default(signal.SIGINT, None)

    try:
        while not queue.empty():
            processed_images = queue.get()
            main_logger.info(f"Parent process received: {processed_images}")
    except Exception as e:
        main_logger.error(f"Parent process encountered an error: {e}")
        sys.exit(1)

    sys.exit(0)
if __name__ == '__main__':
    main(True)
