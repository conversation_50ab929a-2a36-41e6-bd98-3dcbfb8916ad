# Setup Guide for RealMaster FloorPlan Classification Project

This guide outlines the steps necessary to set up the FloorPlan Classification project with Python 3.9, Keras 2.14.0, and TensorFlow 2.14.0.

## Prerequisites

Ensure you have the following dependencies installed on your system:

- **Python 3.9**
- **CUDA 11.8** (for GPU support)
- **cuDNN 8.7.0** (for TensorFlow compatibility)

## Step-by-Step Installation

### 1. Install Python 3.9

Download and install Python 3.9.13:

```bash
wget https://www.python.org/ftp/python/3.9.13/Python-3.9.13.tgz
tar -xvf Python-3.9.13.tgz
cd Python-3.9.13
./configure --prefix=$HOME/python3.9
make
make install
echo 'export PATH=$HOME/python3.9/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### 2. Install Poetry
Poetry is a tool for dependency management and packaging in Python. Install it using the following command:

```bash
pip install poetry
```

### 3. Install Packages
Navigate to your project directory and install the necessary packages:

```bash
poetry install 
```


### 4. Verify CUDA Installation
Check your CUDA installation to ensure it's correctly set up. Follow the detailed guide here: CUDA 11.8 Installation on Ubuntu 22.04.

Verify the installation:

```bash
nvidia-smi
nvcc -V
echo 'export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc
```

### 5. Set Up Configuration
Configure the config/config.ini file based on your environment with the following steps:

- MongoDB Configuration:
Ensure the tlsCAFile parameter is correctly specified in the [mongo] section of the configuration file. This file is required for establishing a secure TLS connection to your MongoDB instance.
<br>

- Kafka Configuration:
Make sure Kafka is running and accessible.
Verify that the necessary Kafka topics are created as specified in the configuration. Ensure both request and response topics are correctly set up and available for use.
<br>


- Local Image Path Configuration:
If you are using local images, set the local_path parameter in the [imageSource] section of the configuration file. This parameter should point to the directory where local images are stored.



### 6. Run the Floorplan Classification Project
```bash
poetry shell
sh run.sh --flc  
# log will be in logs/pyfpimg_flc.log
```


## Testing on ml-dell:
```
cd /home/<USER>/pyfpimg/
poetry shell
sh run.sh --test > logs/pyfpimg_run.log
```



## Run test files:
```
cd pyfpimg/
poetry shell

# you may need set PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:/path/to/your/project/"

python test/mongo_test.py
python test/kafka_producer.py
```


## Common Errors and Solutions
### 1. Model Import Compatibility Issue
If you encounter errors indicating that the model cannot be imported due to compatibility issues, ensure that you are using the same versions of Python, TensorFlow, and Keras as the ones used to train the model. Common error messages include:
If you encounter errors like blow, the model generated can't be imported 不兼容,need use the same python, tensorflow and keras version

```
Layer 'conv2d_11' expected 2 variables, but received 0 variables during loading. Expected: ['conv2d_11/kernel:0', 'conv2d_11/bias:0']
```

```
Error when deserializing class 'InputLayer' using config={'batch_input_shape': [32, 224, 224, 3], 'dtype': 'float32', 'sparse': False, 'ragged': False, 'name': 'rescaling_input'} Exception encountered: You must pass a shape argument.
```

```
ypeError: int() argument must be a string, a bytes-like object, or a real number, not 'list'
Check the code where int() is being used and ensure that it's being passed the correct type of argument. It seems that a list is being passed where an integer is expected. Adjust the code accordingly to resolve this issue.
```

To resolve these issues, ensure that the versions of Python, TensorFlow, and Keras used during model training match the versions being used for model inference.



### 2. CuDNN Library Version Incompatibility
If you encounter errors related to TensorFlow being unable to find a matching CuDNN library version, such as:

 ```
 Loaded runtime CuDNN library: 8.7.0 but source was compiled with: 8.9.4.  CuDNN library needs to have matching major version and equal or higher minor version.
 ```
This indicates that the CuDNN library version being used does not match the version expected by TensorFlow. To resolve this, you may need to install a compatible version of TensorFlow or update your CuDNN library to match the version expected by TensorFlow.