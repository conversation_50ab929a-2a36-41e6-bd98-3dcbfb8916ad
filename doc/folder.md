Folder structure as follow:

```
.
├── README.md                <-- main read me file
├── logs                     <-- logs folder
├── config                   <-- config folder for static value
├── bin                      <-- shell files folder
├── doc
│   ├── change_requirements  <-- change requirements folder
│   ├── db                   <-- coding conventions
│   └── design               <-- design documents
├── model                    <-- final result of models
├── test                     <-- test files
└── src                      <-- src folder for code(add/edit at your pref)
    ├── helpers              <-- helpers function folder
    └── main.py # entry file

```
