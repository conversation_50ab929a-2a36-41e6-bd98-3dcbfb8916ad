### data structure for classsfier result

# input  kafka input json
```
{
  _id:'TRBW118188'
  uaddr:'',
  unt:'',
  phoMt: ISODate("2024-06-25T20:37:12.264+0000")，
  pics:[
    'https://img.realmaster.com/trb/828.1718035089/1/230/C8423230.jpg',
    '/trb/828.1718035089/1/230/C8423230.jpg',
    <!-- #TODO: 路径太短少一级 -->
    'https://img.realmaster.com/cda/828.1717769520/659/27007659_1.jpg',  
    'https://cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCRES/262913989/0/0/0/e537e606990aec2b00601641923daa52/16/e8b27f4103b1011cbf6576428aad0f87/262913989-012A77B8-BA03-41CE-A1D2-6A170394441E.JPG'
  ],
}
```
[]
# kafka output
```
{
  _id:'TRBW118188',
  uaddr:'',
  unt:'',
  phoMt: ISODate("2024-06-25T20:37:12.264+0000")，
  result:[
    {
      input:'<original-Filename-only>', 
      class:'floorplan',
      output:'<path/uuid-filename>'，
      imgHash:"823e13da5390aefbea7b8a5de965ac0dbdc759907e36c2f8b9e673cbd039f28c",
      w：20,
      h：20,  
    }  
    {
      input:'<original-Filename-only>', 
      class:'survey', 
      output:'<path/uuid-filename>',
      imgHash:"bfb",
      w：20,
      h：20  
    } 
    <!-- 返回error的图片 -->
    {
      input:'A', 
      class:'', 
      error:'URL invalid', 
    } 
    {
      input:'B', 
      class:'',
      error:'URL invalid',  
    } 
  ],
  errors:[       
    {
      id:A,
      errorMessage:'URL invalid',
    }
    {
      id:B,
      errorMessage:'URL invalid',
    }
  ]
}
```

# mongo output
```     #楼盘#unt
{
  _id:'uaddr#unt',   not null key _id
  uaddr:'',
  unt:'',
  prop_id:['TRBW118188','TRBW118187','TRBW118189'] #增加
  result:[
    <!-- 10位uuid生成newPath -->
    {input:'<original-Filename-only>', class:'floorplan', output:'<path/uuid-filename>'}  
    {input:'<original-Filename-only>', class:'survey', output:'<path/uuid-filename>'} 
  ]      #增加
}
```