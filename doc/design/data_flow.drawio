<mxfile host="65bd71144e">
    <diagram id="qksLSHWWetUakJMp7ldT" name="Page-1">
        <mxGraphModel dx="1540" dy="481" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="30" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="320" y="150" width="1030" height="410" as="geometry"/>
                </mxCell>
                <mxCell id="55" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="54">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="mongodb.properties" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="-180" y="285" width="70" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="mongodb.result&lt;br&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="1215" y="290" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="39" style="edgeStyle=none;html=1;" parent="1" source="13" target="34" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="677" y="330" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="pictrure download and save in tmp" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="505" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="save files in imageOutput path&lt;br&gt;" style="sketch=0;pointerEvents=1;shadow=0;html=1;strokeColor=default;fillColor=#434445;aspect=fixed;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;shape=mxgraph.vvd.folder;" parent="1" vertex="1">
                    <mxGeometry x="1070" y="450" width="78.94" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="error warning" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=default;fontFamily=Helvetica;fontSize=12;fontColor=default;fillColor=default;" parent="1" vertex="1">
                    <mxGeometry x="706" y="170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="29" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="26" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="29" vertex="1" connectable="0">
                    <mxGeometry x="0.6724" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="&lt;span style=&quot;color: rgb(240, 240, 240); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(42, 37, 47); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;kafka.request&lt;/span&gt;" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;" parent="1" vertex="1">
                    <mxGeometry x="160" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="59" style="edgeStyle=none;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="31" target="58">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="&lt;span style=&quot;color: rgb(240, 240, 240); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(42, 37, 47); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;kafka.response&lt;/span&gt;" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="150" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="35" style="edgeStyle=none;html=1;" parent="1" source="34" target="23" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="36" style="edgeStyle=none;html=1;" parent="1" source="34" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="864" y="330" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="36" vertex="1" connectable="0">
                    <mxGeometry x="-0.0236" y="-3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="47" target="19" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="38" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" parent="1" source="47" target="31" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="floorplan/survey&lt;br&gt;classfier" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;" parent="1" vertex="1">
                    <mxGeometry x="706" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="" style="edgeStyle=none;html=1;" parent="1" source="40" target="42" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="400" y="200"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="43" vertex="1" connectable="0">
                    <mxGeometry x="0.0722" y="1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="img on local?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="360" y="290" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="45" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="42" target="34" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="660" y="200"/>
                            <mxPoint x="660" y="330"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="42" value="get local path" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="505" y="170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="get imgHash and metadata (width and height)" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="1048" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="" style="edgeStyle=none;html=1;" parent="1" source="48" target="51" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="52" vertex="1" connectable="0">
                    <mxGeometry x="-0.1211" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="48" value="is floorplan or survey?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="880" y="285" width="100" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="delete tmp file" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="870" y="170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="56" style="edgeStyle=none;html=1;" edge="1" parent="1" source="54">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="170" y="325" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="54" value="get properties pictures and send kafka request [appweb]&amp;nbsp;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-20" y="295" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="mongodb.floorplans" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="-190" y="430" width="70" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="60" style="edgeStyle=none;html=1;" edge="1" parent="1" source="58" target="57">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="58" value="get results and add wartermark for pictures and store in floorplans [appweb]&amp;nbsp;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-30" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>