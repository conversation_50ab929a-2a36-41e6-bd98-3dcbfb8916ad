### 需求 [revise output path]

## 反馈
1. output path 只保留后2位。
2. 添加时间戳
3. 添加get，save文件的失败处理。
4. 修改流程图。
5. 保存文件surveys设置不同路径。
   

## 需求提出人:   Rain, Fred
## 修改人:       Maggie

## 提出日期
1. 20240822 提出反馈

## 原因
1. output path 需要在appweb kafka 结果处理步骤设置使用。
2. 添加时间戳区分不同时间运行的相同_id。
3. 缺少get，save文件的失败处理。


## 解决办法
1. 将mongo和kafka结果中的均修改output path 只保留后2位，如`T/H6AA0b7ta.jpg`
2. result结果中添加时间戳`ts`。
3. get，save文件均添加10s内连续失败10个，程序退出机制。
4. 添加在imageOutput中添加floorplans_path，surveys_path，根据结果放入对应文件夹。