### 需求 [refactor floorPlan classification]

## 反馈
1. 将运行成功的github上floorPlan classification 按照pyfpimg的结构迁移过来，包括train和predict功能。
2. 训练好的模型应该存在model目录，名字给个日期和准确度。 @Rain 修改为如下格式：FPC_20240611_0948_k2140.h5
3. model位置应该参数指定
4. 模型同时保存其他格式
5. 补充测试floorPlan classification过程中遇到的错误和解决办法
   



## 需求提出人:   Fred
## 修改人:       Maggie

## 提出日期
1. 20240611 提出反馈

## 原因
1. 以前未提供


## 解决办法
1. 按照github上floorplan_classifiction运行时安装步骤完成setup.md文档
2. 将github上floorplan_classifiction目录相关文件拷贝到src/floorplan_classification目录，并在src/main.py中进行调用
3. 自动将生成的model保存到model文件夹，并使用日期（20240611）和f1 score（0.948）来命名文件，如model/FPC_20240611_0948_k2140.keras
4. 添加--model_file 参数，用户可以输入使用的model文件
5. 目前保存.keras, .h5格式。尝试保存SavedModel格式，但报错“Expected an object of type `Trackable`, such as `tf.Module` or a subclass of the `Trackable` class, for export. Got <model.CNN object at 0x7fd6dcf2df40> with type <class 'model.CNN'>.”， 因为SavedModel 格式要求被保存的对象是 Trackable 类的实例，例如 tf.Module 或其子类，现在程序保存的对象是 <model.CNN object at 0x7fd6dcf2df40>，它是一个类型为 <class 'model.CNN'> 的对象，不是 Trackable 类的实例，需要修改源程序做继承，暂时不做。
6. 补充Common Errors and Solutions 到setup.md



