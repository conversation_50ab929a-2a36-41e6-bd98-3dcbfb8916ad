### 需求 [revise for Integration in appweb]

## 反馈
1. 添加图片config用来获取图片
2. 当图片不存在时，程序不终止，只返回error信息
3. Kafka input加入phoMt
4. Kafka response返回错误信息
5. 图片sharp(imgBuffer).metadata() 获取width，height,sha256获取imgHash
6. 添加以后可能会通过ML获取的值
   

## 需求提出人:   Fred
## 修改人:       Maggie

## 提出日期
1. 20240626 提出反馈

## 原因
1. 反馈1为了获取图片
2. 反馈2-5配合appweb上线add_pyfpimg流程


## 解决办法
1. 在config.ini中添加image的local_path和domain，当填写了local_path的值说明是在本机上，直接读取local_path路径下的图片，如果没有填写local_path的值，在图片（/trb/828.1718035089/1/230/C8423230.jpg 或者 trb/828.1718035089/1/230/C8423230.jpg）前加图片服务器domain获取图片。如果是http，https或者double slash（//）开头的图片，直接从网址获取。
  [imageSource]
  local_path =
  url_host = img.realmaster.com
2. 使用Pillow library获取图片的metadata width and height， 并输出到kafka output
3. 使用Sha256计算imgHash，并输出到kafka output
4. error主要有以下10种，所有错误均输出到log中，
    如果是error1，会将error写到Kafka output中，errors会保存到properties表phoClsErr中，
    因为error 2-8都是本程序运行过程中的错误，所以只保留在本程序的log中。
   1. 文件路径失效
   2. floorplan_classification运行时出错
   3. Error moving file 
   4. 删除文件temp路径失败
   5. configure （mongo，kafka producer/consumer，model）失败
   6. sending message to Kafka 
   7. Error writing result to MongoDB
   8. Failed to decode message
5. 当文件路径失效时，不中断程序，输出error message到log，并输出到Kafka output:
```
  {
    _id:'TRBW118188',
    uaddr:'',
    unt:'',
    phoMt: ISODate("2024-06-25T20:37:12.264+0000")，
    result:[
      {
        input:'<original-Filename-only>', 
        class:'floorplan',
        output:'<path/uuid-filename>'，
        imgHash:"823e13da5390aefbea7b8a5de965ac0dbdc759907e36c2f8b9e673cbd039f28c",
        w：20,
        h：20,  
      }  
      {
        input:'<original-Filename-only>', 
        class:'survey', 
        output:'<path/uuid-filename>',
        imgHash:"bfb",
        w：20,
        h：20  
      } 
      <!-- 返回error的图片 -->
      {
        input:'A', 
        class:'', 
        error:'URL invalid', 
      } 
      {
        input:'B', 
        class:'',
        error:'URL invalid',  
      } 
    ],
    errors:[       
      {
        input:'A',
        errorMessage:'URL invalid',
      }
      {
        input:'B',
        errorMessage:'URL invalid',
      }
    ]
  }
```
1. 添加最新的模型文件，根据模型的重新训练和评估（https://zbdl6uthb0f.larksuite.com/wiki/LCdBwdUSNiMk6VkTE6fuyHRBsqf），选取目前准确率最高的模型，增加模型文件FPC_20240621_0982_k2140.keras 和 FPC_20240621_0982_k2140.h5
2. 简化部分floorplan_classification代码：
   1. 删除load_dataset_train_preds_chain function,直接在main function中拆分成train_save和predict流程；
   2. 将原来的提取pdf相关function修改为extract_high_res_images_from_pdf_dir function
   3. 将copy_sur，copy_other function合并为一个copy_images

