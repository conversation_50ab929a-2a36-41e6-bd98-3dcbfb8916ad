### 需求 [init]

## 反馈
1. 作floorplan的识别和保存。在Seneca的代码基础上，我们另建一个repo。 就是读mongodb，过滤图片，识别出floor plan，backup图片到指定folder，记录db。今后再增加Kafka consumer实时识别新房源。 写入的数据格式，可以和罗晓伟的一致，folder可以不同。
2. 也可以直接就是kafka，然后用另一个程序读mongo，发图片位置给这个程序
   



## 需求提出人:   Fred
## 修改人:       Maggie

## 提出日期
1. 20240610 提出反馈1

## 原因
1. 以前未提供


## 解决办法
1. 设计图见./design/design.drawio
2. 通过kafka获取数据，通过python处理数据，写回到kafka和mongodb(local),数据格式见db/floorplan_result.md, 
3. 在本项目，不对图片加水印和其他处理
