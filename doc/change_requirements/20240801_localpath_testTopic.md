### 需求 [revise for Integration in appweb]

## 反馈
1.localmode文件path去掉 /trb/1718290348 只保留/1/832/W8436832.jpg， 前两位是类型和时间戳
2.Pyfpimg需要改下测试文件, topic 用测试topic， 不要用正式topic，如
_test_topic_name
3.处理Subscribed topic not available: fpimg-request-init: Broker: Unknown topic or partition" error
4.+个可以单独测试mongodb connection的mongo.py
   

## 需求提出人:   Rain
## 修改人:       Maggie

## 提出日期
1. 20240801 提出反馈

## 原因
1. localmode文件path去掉类型和时间戳
2. test topic 和 运行topic分开
3. 增加mongodb connection的测试


## 解决办法
1. 在get_local_img（）去掉前两位path
2. 增加_test_topic, 程序增加test模式，使用_test_topic
3. 增加topic相关报错处理
4. 增加test文件mongo.py