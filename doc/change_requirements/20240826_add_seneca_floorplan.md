### 需求 [add seneca floorplan]

## 反馈
Seneca floorplan 改写为主要使用GPT
Direction Classification - used to classify directions signs in the image.
Unit Number OCR - used to extract unit numbers from key plate and to extract direction for each unit number
Room Floor Area OCR - used to extract area (total and inner), floor numbers and number of rooms for floorplan image.

接受一个folder为input，自动处理里面的pdf和图片，生成的图片存到image_output文件夹。错误文件，转移到error文件夹。exposure测试一下，如果没法得到合适结果，可以暂不处理。
测试3个AI，测试batch


## 需求提出人:   Fred, Rain
## 修改人:       Maggie


## 提出日期
1. 20240826 提出反馈


## 原因
1. 以前未提供


## 解决办法
1. 输入为一个文件夹：pyfpimg.pdfSource.local_path
   1. 文件名称 addr([_|\s+]city([_\s+]prov))([-\s+]project-name)，addr中不能有`-`或者`_` [125 Peter Street_Toronto_ON-projectA.pdf]
   2. 3种情况：
      1. pdf 在外层folder  
      2. folder里面是一组图片，folder名是这样的。
      3. folder里面是pdf，folder名是这样的。
   
2. 根据输入文件，将里面的pdf转成img，并生成`pdf_list`，并将不含有`encoded_images`的数据存入mongo，文件名称已修改为uuid1/uuid9.jpeg，[不需要再运行batch src/batch/condos/fix_floorplanId.coffee]
   
3. 运行时读取文件夹内的文件，如果遇到文件名错误，或者文件有问题，将文件保存到error_img_path，并将错误信息，保存的error_img_path下的error.log
   
4. 根据`pdf_list`生成jsonl，并create batch，因为batch默认是24小时完成，程序 1min, 1min, 2min, 5min, 10 min, 30 min, then 1 hour repeatedly(for total 24h)，check batch状态，24小时仍未完成，超时退出。 测试时6个任务，通常几分钟就完成。[openAI batch](https://platform.openai.com/docs/guides/batch/overview) 24小时内运行完成。
   
5. `pdf_list` and mongo 记录在 floorplanC:
```
{
    "_id" : ObjectId("66dfb27fe8c5b98b7422460b"),
    "addr" : "23 at Babypoint Towns",
    "ai_model" : "gpt-4o",
    "ai_name" : "openai",
    "city" : "Toronto",
    "images" : [
        "g/PnjggUQHP.png"
    ],
    "encoded_images":[""] # only in `pdf_list`
    "ai_result":[
        ""
    ]
    "project_name" : "projB",
    "prov" : "ON",
    "ts" : ISODate("2024-09-10T13:55:07.711+0000")
    "is_folder": false,
    "all_files" : [ // List of files
    {
        "file_path": "/pyfpimg/test/floor_plan_pdf/TheHarlowe-proF.png",
        "file_type": "image" // or "pdf"
    },
    // Other files...
    ]
}
```

6. 输出根据config设置:pyfpimg.pdfImageOutput
   1. result_file:结果json
   2. img_path
   3. error_img_path


7. 重新运行的，结果文件只含有新结果
8. condo表里unit_no是一个值。如果一个图中有2个unt（相同nm）写2条floorplan结果
9.  appweb 保存到mongo condos:
```
{
    "_id" : "CA:ON:MARKHAM:1 GRANDVIEW AVE",
    "addr" : "1 Grandview Ave",
    "city" : "Markham",
    "prov" : "On",
    "status" : NumberInt(3),                         #no
    "_mt" : ISODate("2024-08-09T03:47:50.923+0000"),
    "amen" : [   #no
        "Concierge",
    ],
    "amen_extra" : [ #no
        "Sauna",
    ],
    "cnts" : NumberInt(364),                #no, vc查看数量/cnts统计数量
    "constr" : [ #no
        "Concrete"
    ],
    "gatp" : [ #no
        "Undergrnd"
    ],
    "heat" : [ #no
        "Forced Air"
    ],
    "mfee_inc" : [ #no
        "Insurance",
        "Heat"
    ],
    "mfee_sqft" : [ #no
        0.5
    ],
    "prop_mgmt" : [ #no
        "Forest Hill Kipling"
    ],
    "spec" : [ #no
        "Other",
        "Accessibility"
    ],
    "vcnts" : NumberInt(67561),                      #no,查看数量
    "age" : NumberInt(2022), #no
    "desc" : "The Vanguard Condominiums was developed by Devron Developments. This Markham condo sits near Steeles Ave and Yonge St, in Markham's Grandview neighbourhood. Spread out over 27 stories, suites at The Vanguard Condominiums range in size from 522 to 2460 sqft. This Markham condo has 213 condo units and can be found at 1 Grandview Avenue. The Vanguard Condominiums's amenities include a Yoga Studio and Guest Suites.", #no
    "img" : { #no
        "l" : [
            "P/C/BOLQ.jpeg",
            "P/C/BOLR.jpeg"
        ]
    },
    "nm" : "The Vanguard Condominiums",       # projectName
    "source" : "https://condos.ca; https://www.buzzbuzzhome.com", # pdf+img  all_files[]
    "src": "floorplanC"      # new
    "stories" : NumberInt(27), #no  楼层
    "units" : NumberInt(213), #no 总户
    "floorplan" : [
        {
            "nm" : "PH07",
            "unit_no" : "4",
            "bdrms" : "2.5",
            "bthrms" : "2",
            "img" : "P/C/BOOF.png", #1/9
            "fce" : [
                "N",
                "E"
            ],
            "lvl" : [
                "2"
            ],
            "id" : "VpK5aOIJnK0StWI4",         #nanoId(16)
            "price" : "1870", #no
            "sqft" : "2034990"
        },
        # add new
    ],
    "dvlpr" : " Devron Developments" #Developer no
}
```




prompt：
You are a real estate expert. Based on the provided floorplan image, extract and provide the following details for each image: the floor range(the floor number marked in the small image looks like FLoors3,FLoors4-8), unit number (the room number specially marked in the small image), orientation, square footage (sqft), and floorplan name. If there are multiple floors, suites, or different square footages, list all of them. Usually, a narrow strip outside the house represents the balcony, and the balcony's orientation is considered the room's orientation. The small image represents the layout of all rooms on the same floor. Provide the orientation of the specially marked room, specifying the cardinal direction (e.g., N,NE,NW) only if it aligns exactly with the arrow. If the direction is between two cardinal directions, specify it as Northeast, Northwest, etc.

AI return:
{
    "nm": "",                                  // name
    "bdrms": "2.5",                            // bedroom number
    "bthrms": "2",                             // bathroom number
    "sqft": "1870",                           // not include balcony
    "unts": [
        {
            "unt": "2",    // unit number
            "lvl": "3",   // floor range
            "fce": "NE"     // orientation
        },
        {
            "unt": "3",
            "lvl": "6-10",
            "fce": "N"
        }
    ]
}
For each image generate a JSON structure like this:Finally, combine all in order into a list json.Only return the json.

