[pyfpimg.imageOutput]
floorplans_path = "/tmp/floorplans"
surveys_path = "/tmp/surveys"

[pyfpimg.imageSource]
local_path_cda = "/mnt/md0/mlsimgs/crea/ddf/img/"
local_path_creb = "/mnt/md0/mlsimgs/creb/mls/"
local_path_orb = "/mnt/md0/mlsimgs/oreb/mls/"
local_path_oreb = "/mnt/md0/mlsimgs/oreb/mls/"
local_path_trb = "/mnt/md0/mlsimgs/treb/mls/"
url_host = "img.realmaster.com"

[pyfpimg.kafka.request]
_test_topic_init = "test-fpimg-request-init"
_test_topic_routine = "test-fpimg-request-routine"
auto_offset_reset = "earliest"
bootstrap_servers = "127.0.0.1:9092"
group_id = "my-group"
timeout = 60
topic_init = "fpimg-request-init"
topic_routine = "fpimg-request-routine"
worker_num = 4

[pyfpimg.kafka.response]
_test_topic_name = "test-fpimg-response"
bootstrap_servers = "127.0.0.1:9092"
topic_name = "fpimg-response"

[pyfpimg.log]
file = "./logs/pyfpimg.log"
flc_file = "./logs/flc.log"
level = "INFO"
#level = "DEBUG"

[pyfpimg.model]
name = "FPC_20240621_0982_k2140.h5"
path = "model"

[pyfpimg.mongo]
collection = "floorplan_classification"
host = "127.0.0.1"
name = "listing"
password = "d1"
port = 27_017
tlsCAFile = "/etc/mongo/ca.crt"
user = "d1"

[pyfpimg.openAI]
key = "*****************************************************************************************************************************************************************"
endpoint = "https://api.openai.com/v1/chat/completions"
batchUrl = "/v1/chat/completions"
orgID = "org-XknsRihTPUr9OozG2H5byyOQ"
projectID = "proj_yPZHSJr6CWsj39U4jBvHfpdv"
#model = "gpt-4o-mini"
model = "gpt-4o" # use for production
#model = "gpt-4.1-nano" # use for testing
# batch mode: sequential(single), line_by_line(total jsonl + line by line), small_batch(small batch), large_batch(large batch - original way)
batch_mode = "line_by_line"
# small batch size
small_batch_size = 3
# keep total jsonl file (true/false)
keep_master_jsonl = true

[pyfpimg.gemini]
key = "AIzaSyBHoyzQTkMF_Aps9ykYZ00BRHdtfGc_TRk"
#model = "gemini-1.5-flash"
model = "gemini-1.5-pro"

[pyfpimg.claude]
key = "************************************************************************************************************"
endpoint = "https://api.anthropic.com/v1/messages"
model = "claude-3-haiku-20240307"

[pyfpimg.pdfImageOutput]
result_file = "./test/floor_plan_img/floor_plan_img_result.json"
img_path = "./test/floor_plan_img"
error_img_path = "./test/floor_plan_img_error"

[pyfpimg.pdfSource]
local_path = "./test/floor_plan_pdf"

[pyfpimg.mongoFLC]
collection = "floorplanC"
host = "127.0.0.1"
name = "listing"
password = "d1"
port = 27_017
tlsCAFile = "/etc/mongo/ca.crt"
user = "d1"

[pyfpimg.AI]
name = "openai"
model = "gpt-4o"
#model = "gpt-4o-mini"