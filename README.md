# README #

- Floorplan image recognizer. Listen on kafka topics to sort out if a url or local file is a floorplan, survey or other, then save it to specified folder(s). 

- Floorplan seneca (--flc) gets floorplan information from floorplan img or pdf files using AI and store into a json file which will be used in appweb

### What is this repository for? ###

* Floor plan classifier
* 0.0.1
* [Learn Markdown](https://bitbucket.org/tutorials/markdowndemo)

### How do I get set up? ###

* Setup: see setup.md
* Configuration: see conf.ini
* Dependencies: see poetry.toml
  * kafka
  * mongodb
  * python
  * keras
* Database configuration: see conf.ini
* How to run tests
* Deployment instructions: setup.md


### How to run it? ###
- floorplanB:
  - test: sh run.sh -f ./config/config.ini --t
  - prod: sh run.sh -f ./config/config.ini
- floorplanC:
  - sh run.sh --flc