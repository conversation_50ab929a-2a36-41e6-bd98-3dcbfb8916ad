# README #

Floorplan image recognizer. Listen on kafka topics to sort out if a url or local file is a floorplan, survey or other, then save it to specified folder(s). Add a watermark and copy to another folder if needed.

### What is this repository for? ###

* Floor plan classifier
* 0.0.1
* [Learn Markdown](https://bitbucket.org/tutorials/markdowndemo)

### How do I get set up? ###

* Setup: see setup.md
* Configuration: see conf.ini
* Dependencies: see poetry.toml
  * kafka
  * mongodb
  * python
  * keras
* Database configuration: see conf.ini
* How to run tests
* Deployment instructions: setup.md

### Contribution guidelines ###

* Writing tests
* Code review
* Other guidelines

### Who do I talk to? ###

* Repo owner or admin
* Other community or team contact