[tool.poetry]
# package-mode = false
name = "pyfpimg"
version = "0.0.1"
description = "Floorplan image recognizer. Listen on kafka topics to sort out if a url or local file is a floorplan, survey or other, then save it to specified folder(s). Add a watermark and copy to another folder if needed."
authors = ["<PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.9,<3.12"
tensorflow = "2.14.0"
pytesseract = "^0.3.10"
opencv-python = "^*********"
easyocr = "^1.7.1"
pdf2image = "^1.17.0"
imagehash = "^4.3.1"
pymupdf = "1.24.4"
fitz = "^0.0.1.dev2"
torch = "^2.3.1"
torchvision = "^0.18.1"
torchaudio = "^2.3.1"
scikit-learn = "1.4.2"
matplotlib = "3.8.4"
frontend = "^0.0.3"
tools = "^0.1.9"
keras = "2.14.0"
pymongo = "^4.7.3"
confluent-kafka = "^2.4.0"
numpy = "1.26.4"
nanoid = "^2.0.0"
# tensorflow-macos = "2.14.0"  # only for testing on macos
toml = "^0.10.2"
DateTime = "^5.5"
google-generativeai = "^1.15.0"
openai = "^1.93.0"



[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
