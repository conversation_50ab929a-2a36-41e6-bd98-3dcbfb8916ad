"""
convert_pdf_to_images.py

Key to High-Fidelity Conversion
DPI Setting: Setting dpi=300 means 300 dots per inch, which is a common high-resolution setting suitable for scenarios requiring high-quality images. The DPI value can be adjusted as needed, typically ranging between 300 and 600.

Zoom Factor: High resolution is achieved by setting the zoom factors zoom_x and zoom_y. The ratio dpi / 72.0 is commonly used for conversion since the default DPI is usually 72.
"""

import os
import fitz  # PyMuPDF
from pathlib import Path
import argparse


def remove_file(filepath):
    """Remove file if it exists."""
    try:
        os.remove(filepath)
    except FileNotFoundError:
        pass


def extract_high_res_images_from_pdf_dir(pdf_dir, output_folder, prefix="pdf_page", dpi=300):
    """
    Extract high-resolution images from all PDFs in a directory and save them as separate files in the output folder.

    :param pdf_dir: Path to the directory containing PDF files.
    :param output_folder: Folder where the images will be saved.
    :param prefix: Prefix for the saved image files.
    :param dpi: Resolution of the output images (dots per inch).
    """
    # Ensure the output folder exists
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # Iterate over each PDF file in the directory
    for pdf_file in Path(pdf_dir).rglob('*.pdf'):
        try:
            pdf_filename = pdf_file.stem  # Get the PDF file name without extension
            prefix = pdf_filename  # Set the prefix to the PDF file name
            pdf_doc = fitz.open(pdf_file)

            try:
                # Iterate over each page in the PDF
                for page_index, page in enumerate(pdf_doc):
                    # Create the file path for the output image
                    filepath = os.path.join(
                        output_folder, f'{prefix}_{page_index + 1}.png')

                    # Get the page and convert it to an image
                    zoom_x = dpi / 72.0  # Horizontal zoom factor
                    zoom_y = dpi / 72.0  # Vertical zoom factor
                    matrix = fitz.Matrix(zoom_x, zoom_y)
                    # Use the matrix for high resolution
                    pix = page.get_pixmap(matrix=matrix, alpha=False)

                    # Remove the existing file if it exists
                    remove_file(filepath)

                    # Save the image
                    pix.save(filepath)

                    print(f"Saved {filepath}")
            finally:
                pdf_doc.close()
        except Exception as e:
            print(f"Error processing PDF file {pdf_file}: {str(e)}")

def main():
    parser = argparse.ArgumentParser(
        description='Extract high-resolution images from PDF files in a directory.')
    parser.add_argument('pdf_dir', type=str,
                        help='Path to the directory containing PDF files.')
    parser.add_argument('output_folder', type=str,
                        help='Output folder where the images will be saved.')
    parser.add_argument('--dpi', type=int, default=300,
                        help='Resolution of the output images (dots per inch).')

    args = parser.parse_args()

    extract_high_res_images_from_pdf_dir(
        args.pdf_dir, args.output_folder, dpi=args.dpi)


if __name__ == "__main__":
    main()
