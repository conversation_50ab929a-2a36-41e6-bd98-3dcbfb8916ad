"""
convert_pdf_to_images.py

Key to High-Fidelity Conversion
DPI Setting: Setting dpi=300 means 300 dots per inch, which is a common high-resolution setting suitable for scenarios requiring high-quality images. The DPI value can be adjusted as needed, typically ranging between 300 and 600.

Zoom Factor: High resolution is achieved by setting the zoom factors zoom_x and zoom_y. The ratio dpi / 72.0 is commonly used for conversion since the default DPI is usually 72.
"""

import os
import fitz  # PyMuPDF
from pathlib import Path
import argparse
import base64
from src.floorplan_classification.image_wrangling import extract_high_res_images_from_pdf_dir



def remove_file(filepath):
    """Remove file if it exists."""
    try:
        os.remove(filepath)
    except FileNotFoundError:
        pass


def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def main():
    parser = argparse.ArgumentParser(
        description='Extract high-resolution images from PDF files in a directory.')
    parser.add_argument('pdf_dir', type=str,
                        help='Path to the directory containing PDF files.')
    parser.add_argument('output_folder', type=str,
                        help='Output folder where the images will be saved.')
    parser.add_argument('--dpi', type=int, default=300,
                        help='Resolution of the output images (dots per inch).')


    args = parser.parse_args()
    pdf_list = extract_high_res_images_from_pdf_dir(
         args.pdf_dir, args.output_folder, dpi=args.dpi)
    # print(pdf_list)


if __name__ == "__main__":
    main()
