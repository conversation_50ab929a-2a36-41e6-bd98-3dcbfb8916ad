"""
rotate_images.py

This script rotates images in a specified input folder by 90, 180, and 270 degrees, and saves them in an output folder with names indicating the rotation angle.

Instructions:
Run the script with the input and output folder paths as arguments. For example:
   python rotate_images.py /path/to/input/folder /path/to/output/folder

Requirements:
- Ensure you have the Pillow library installed. You can install it using pip:
  pip install Pillow
"""
import gc
import os
from PIL import Image
import sys


def rotate_image(image_path, output_folder):
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found - {image_path}")

    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    base_name = os.path.basename(image_path)
    name, ext = os.path.splitext(base_name)

    try:
        with Image.open(image_path) as image:
            for angle in [90, 180, 270]:
                rotated_image = image.rotate(angle, expand=True)
                if rotated_image.mode != 'RGB':
                    rotated_image = rotated_image.convert('RGB')
                rotated_image_name = f"{name}-r{angle}{ext}"
                rotated_image_path = os.path.join(output_folder, rotated_image_name)
                rotated_image.save(rotated_image_path)
                print(f"Saved rotated image: {rotated_image_path}")
        gc.collect()
        return True
    except (IOError, OSError) as e:
        raise RuntimeError(f"Error processing image {image_path}: {str(e)}")
    except Image.DecompressionBombError:
        raise MemoryError(f"Image {image_path} is too large to process")
    except Exception as e:
        print(f"Error rotating image {image_path}: {str(e)}")


def process_images(input_folder, output_folder):
    if not os.path.exists(input_folder):
        print(f"Error: Input folder not found - {input_folder}")
        return
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    for filename in os.listdir(input_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            try:
                image_path = os.path.join(input_folder, filename)
                rotate_image(image_path, output_folder)
            except Exception as e:
                print(f"Error processing image {filename}: {str(e)}")


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python rotate_images.py <input_folder> <output_folder>")
        sys.exit(1)

    input_folder = sys.argv[1]
    output_folder = sys.argv[2]

    process_images(input_folder, output_folder)
